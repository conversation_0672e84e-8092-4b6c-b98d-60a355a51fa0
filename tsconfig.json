{
  "compilerOptions": {
      "target": "esnext",
      "module": "esnext",
      "strict": true,
      "jsx": "preserve",
      "moduleResolution": "node",
      "skipLibCheck": true,
      "esModuleInterop": true,
      "allowSyntheticDefaultImports": true,
      // "forceConsistentCasingInFileNames": true,
      "strictPropertyInitialization": false,
      "useDefineForClassFields": true,
      "sourceMap": true,
      "allowJs": true,
      "checkJs": false,
      "baseUrl": ".",
      "types": ["webpack-env"],
      "paths": {
          "@/*": ["src/*"]
      },
      "lib": ["esnext", "dom", "dom.iterable", "scripthost"]
  },
  "include": [
      "src/**/*.ts",
      "src/**/*.tsx",
      "src/**/*.vue",
      "tests/**/*.ts",
      "tests/**/*.tsx",
      "components.d.ts",
      "auto-imports.d.ts"
  ],
  "exclude": ["node_modules"]
}
