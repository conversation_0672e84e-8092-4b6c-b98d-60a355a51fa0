{
  "compilerOptions": {
    "target": "esnext",
    "module": "esnext",
    "strict": true,
    "jsx": "preserve",
    "importHelpers": true,
    "moduleResolution": "node",
    "skipLibCheck": true,
    "esModuleInterop": true,
    "declaration": true,
    "declarationDir": "./lib/types/",
    "allowSyntheticDefaultImports": true,
    "strictPropertyInitialization": false,
    "sourceMap": true,
    "experimentalDecorators":true,
    "baseUrl": ".",
    "types": [
      "webpack-env"
    ],
    "paths": {
      "@/*": [
        "src/*"
      ]
    },
    "lib": [
      "esnext",
      "dom",
      "dom.iterable",
      "scripthost"
    ]
  },
  "include": [
    "src/",
  ],
  "exclude": [
    "node_modules"
  ]
}