/**
 * 将可能得颜色字符串转为阅片软件需要的8位16进制 后2位为透明度
 * ex: red #fff rgb(0, 0, 0) 转为 00ff00ff 这种类型
 * @param color 
 * @param alphaAtTheEnd 
 * @returns 
 */
export const convertColorToHex = (color: string, alphaAtTheEnd = false): string => {
  if (color?.startsWith('#')) {
    const hex = color.slice(1);
    let expandedHex = '';

    // 处理简写形式（3或4位）
    if (hex.length === 3 || hex.length === 4) {
      expandedHex = hex
        .split('')
        .map((c) => c + c)
        .join('');
    } else if (hex.length === 6 || hex.length === 8) {
      expandedHex = hex;
    } else {
      throw new Error(`Invalid hex color: ${color}`);
    }

    // 补全到8位
    if (expandedHex.length === 6) expandedHex += 'ff';
    else if (expandedHex.length !== 8) throw new Error(`Invalid hex color: ${color}`);

    // 解析RGBA
    const r = parseInt(expandedHex.substr(0, 2), 16);
    const g = parseInt(expandedHex.substr(2, 2), 16);
    const b = parseInt(expandedHex.substr(4, 2), 16);
    const a = parseInt(expandedHex.substr(6, 2), 16);

    if ([r, g, b, a].some(isNaN)) throw new Error(`Invalid hex color: ${color}`);
    if (alphaAtTheEnd) {
      return `#${toHex(r)}${toHex(g)}${toHex(b)}${toHex(a)}`.toUpperCase();
    } else {
      return `#${toHex(a)}${toHex(r)}${toHex(g)}${toHex(b)}`.toUpperCase();
    }
  } else {
    // 通过DOM获取计算后的RGBA值
    const rgbaStr = getComputedRgba(color);
    const { r, g, b, a } = parseRgba(rgbaStr);
    if (alphaAtTheEnd) {
      return `#${toHex(r)}${toHex(g)}${toHex(b)}${toHex(a)}`.toUpperCase();
    } else {
      return `#${toHex(a)}${toHex(r)}${toHex(g)}${toHex(b)}`.toUpperCase();
    }
  }
};

// 辅助函数：数值转两位十六进制
const toHex = (n: number): string => n.toString(16).padStart(2, '0');

// 获取计算后的RGBA字符串
function getComputedRgba(color: string): string {
  const div = document.createElement('div');
  div.style.color = color;
  div.style.display = 'none';
  document.body.appendChild(div);
  const computedColor = window.getComputedStyle(div).color;
  document.body.removeChild(div);

  // 转换RGB为RGBA
  return computedColor.startsWith('rgb(')
    ? computedColor.replace(')', ', 1)').replace('rgb', 'rgba')
    : computedColor;
}

// 解析RGBA字符串
function parseRgba(rgbaStr: string): { r: number; g: number; b: number; a: number } {
  const match = rgbaStr.match(/rgba?\(([^)]+)\)/);
  if (!match) throw new Error(`Invalid RGBA format: ${rgbaStr}`);

  const components = match[1].split(',').map((c) => c.trim());
  if (components.length < 3) throw new Error(`Invalid RGBA format: ${rgbaStr}`);

  // 解析颜色分量
  const parseComponent = (c: string, isAlpha = false): number => {
    if (c.endsWith('%')) {
      const percent = parseFloat(c);
      return isAlpha ? percent / 100 : Math.round(percent * 2.55);
    }
    const value = parseFloat(c);
    return isAlpha ? value : Math.max(0, Math.min(255, value));
  };

  const r = parseComponent(components[0]);
  const g = parseComponent(components[1]);
  const b = parseComponent(components[2]);
  let a = components[3] ? parseComponent(components[3], true) : 1;

  a = Math.round(Math.max(0, Math.min(1, a)) * 255);
  return { r, g, b, a };
}
