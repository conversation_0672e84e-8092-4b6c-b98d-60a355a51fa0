// 随机颜色
export function getRandomHexColor() {
  var letters = '0123456789ABCDEF';
  var color = '#';
  for (var i = 0; i < 6; i++) {
    color += letters[Math.floor(Math.random() * 16)];
  }
  return color;
}

export function Min(x: number, y: number): number {
  return x - y < 0 ? x : y;
}

export function sanitizeName(name: string) {
  return name.replace(/[^\w\s\u4e00-\u9fa5]/gi, '');
}

export function matchReg(str?: string) {
  if (str) {
    let reg = /<\/?.+?\/?>/g;
    const newStr = str?.replace(reg, '');
    return newStr;
  }
}
