import { convertColorToHex } from './color';
import { imageCoordinatesToViewCoordinates } from './coordinate';
import { generateId } from './index';
import { Group, LabelsDTO } from '@/views/Marker/components/pixi/pixi.types';
import { v4 as uuidv4 } from 'uuid';
import { DrawCname } from '../views/Marker/components/editorEnum';

/**
 * 将切片库的JSON转为阅片软件JSON
 */

const loopGroup = (group: any) => {
  if (group?.length > 0) {
  }
};

export const localJSONToRadingSoftwareJSON = (labels: any[]): LabelsDTO => {
  const softwareJSON: any = {
    Version: 'V2.1',
    GroupModel: {
      Name: 'root',
      Color: '',
      Groups: [],
      Labels: [],
      AdditionalInformation: null,
    },
  };

  const labelsResult: any = labels
    .map((item: any) => {
      if (item.name === 'rect') {
        return localRectToReading(item);
      } else if (item.name === 'ellipse') {
        return localElipsToReading(item, false);
      } else if (item.name === 'circle') {
        return localElipsToReading(item, true);
      } else if (item.name === 'arrow') {
        return localArrowToReading(item);
      } else if (
        item.name === 'line' ||
        item.name === 'foldLine' ||
        item.name === 'polygon' ||
        item.name === 'closedCurve' ||
        item.name === 'freeCurve'
      ) {
        if (item.name === 'closedCurve' || item.name === 'freeCurve') {
          item.type = 'btn_brush';
        }
        if (item.name === 'foldLine' || item.name === 'line' || item.name === 'polygon') {
          item.type = 'btn_pen';
        }
        const needClosed = item.name === 'closedCurve' || item.name === 'polygon';
        return localLineToReading(item, needClosed);
      } else if (item.name === 'ring') {
        return localRingToReading(item);
      } else if (item.name === 'angle') {
        return localAngleToReading(item);
      } else if (item.name === 'flag') {
        return localFlagToReading(item);
      } else {
        return {
          Type: null,
        };
      }
    })
    .filter((item) => item.Type);
  const GroupsMap: any = {};
  const GroupsResult: any[] = [];
  const rootLabels: any[] = [];

  for (const item of labelsResult) {
    const name = item.PartOfGroup;
    if (!GroupsMap[name]) {
      GroupsMap[item.PartOfGroup] = {
        Name: item.PartOfGroup,
        Color: null,
        Groups: [],
        Labels: [item],
      };
    } else {
      GroupsMap[item.PartOfGroup].Labels.push(item);
    }
  }

  for (const item of labelsResult) {
    const parent = JSON.parse(item.Tag)?.attrs?.parentGroup;
    const name = item.PartOfGroup;

    // 处理没有组名或者为root的
    if (!name || name === 'root') {
      rootLabels.push(item);
      continue;
    }
    if (!parent) {
      const index = GroupsResult.findIndex((item: any) => item.Name === name);
      if (index === -1) {
        GroupsResult.push(GroupsMap[name]);
      }
    } else {
      // 有父级
      if (!GroupsMap[parent]) {
        rootLabels.push(item);
        continue;
      }

      const index = GroupsMap[parent]?.Groups?.findIndex((item: any) => item.Name === name);
      if (index === -1) {
        GroupsMap[parent].Groups.push(GroupsMap[name]);
      }

      // 但是父级已经被删除
    }
  }
  softwareJSON.GroupModel.Groups = GroupsResult;
  softwareJSON.GroupModel.Labels = rootLabels;
  return softwareJSON;
};

const localRectToReading = (item: any) => {
  const { attrs } = item;
  const { pixelCoords = {} } = attrs;
  const { imagePoints = [] } = pixelCoords;
  const tag = {
    id: attrs.id,
    tag: item.tag,
    attrs: attrs,
  };
  return {
    Name: null,
    ID: generateId(Number(attrs.id)),
    Type: 'btn_rect',
    PartOfGroup: attrs.group,
    Coordinates: handleImagePoints(imagePoints),
    LineColor: convertColorToHex(attrs.stroke),
    LineWidth: attrs.strokeWidth,
    FontSize: 12,
    FontColor: '#FF000000',
    Area: 0,
    Description: attrs.Description || '',
    FillBackgroundColor: null,
    Opacity: 0,
    Tag: JSON.stringify(tag),
  };
};

const localElipsToReading = (item: any, isCricle: boolean) => {
  const { attrs } = item;
  const { pixelCoords = {} } = attrs;
  const { imagePoints = [] } = pixelCoords;
  const tag = {
    id: attrs.id,
    tag: item.tag,
    attrs: item.attrs,
    isCricle: isCricle,
  };
  return {
    Name: null,
    ID: generateId(Number(attrs.id)),
    Type: 'btn_elips',
    PartOfGroup: attrs.group,
    Coordinates: handleImagePoints(imagePoints),
    LineColor: convertColorToHex(attrs.stroke),
    LineWidth: attrs.strokeWidth,
    FontSize: 12,
    FontColor: '#FF000000',
    Area: 0,
    Description: attrs.Description || '',
    FillBackgroundColor: null,
    Opacity: 0,
    Tag: JSON.stringify(tag),
  };
};

const localArrowToReading = (item: any) => {
  const { attrs } = item;
  const { pixelCoords = {} } = attrs;
  const { imagePoints = [] } = pixelCoords;
  const tag = {
    id: attrs.id,
    tag: item.tag,
    attrs: item.attrs,
  };
  return {
    Name: null,
    ID: generateId(Number(attrs.id)),
    Type: 'btn_arrow',
    PartOfGroup: attrs.group,
    Coordinates: handleImagePoints(imagePoints),
    LineColor: convertColorToHex(attrs.stroke),
    LineWidth: attrs.strokeWidth,
    FontSize: 12,
    FontColor: '#FF000000',
    Area: 0,
    Description: attrs.Description || '',
    FillBackgroundColor: null,
    Opacity: 0,
    Tag: JSON.stringify(tag),
  };
};
const localLineToReading = (item: any, needClosed = false) => {
  const { attrs } = item;
  const { pixelCoords = {} } = attrs;
  let { imagePoints = [] } = pixelCoords;
  imagePoints = needClosed ? [...imagePoints, imagePoints[0], imagePoints[1]] : imagePoints;
  const tag = {
    id: attrs.id,
    tag: item.tag,
    attrs: item.attrs,
    name: item.name,
    cName: item.cName,
  };
  return {
    Name: null,
    ID: generateId(Number(attrs.id)),
    Type: item.type,
    PartOfGroup: attrs.group,
    Coordinates: handleImagePoints(imagePoints),
    LineColor: convertColorToHex(attrs.stroke),
    LineWidth: attrs.strokeWidth,
    FontSize: 12,
    FontColor: '#FF000000',
    Area: 0,
    Description: attrs.Description || '',
    FillBackgroundColor: null,
    Opacity: 0,
    Tag: JSON.stringify(tag),
  };
};
const localAngleToReading = (item: any) => {
  const { attrs } = item;
  const { pixelCoords = {} } = attrs;
  let { imagePoints = [] } = pixelCoords;
  const tag = {
    id: attrs.id,
    tag: item.tag,
    attrs: item.attrs,
  };
  return {
    Name: null,
    ID: generateId(Number(attrs.id)),
    Type: 'btn_angle',
    PartOfGroup: attrs.group,
    Coordinates: handleImagePoints(imagePoints),
    LineColor: convertColorToHex(attrs.stroke),
    LineWidth: attrs.strokeWidth,
    FontSize: 12,
    FontColor: '#FF000000',
    Area: 0,
    Description: attrs.Description || '',
    FillBackgroundColor: null,
    Opacity: 0,
    Tag: JSON.stringify(tag),
  };
};
const localFlagToReading = (item: any) => {
  const { attrs } = item;
  const { pixelCoords = {} } = attrs;
  const { imagePoints = [] } = pixelCoords;
  const tag = {
    id: attrs.id,
    tag: item.tag,
    attrs: item.attrs,
  };
  return {
    Name: null,
    ID: generateId(Number(attrs.id)),
    Type: 'btn_flag',
    PartOfGroup: attrs.group,
    Coordinates: [],
    LineColor: '#FFFF0000',
    LineWidth: 1,
    FontSize: 12,
    FontColor: '#FF000000',
    Area: 0,
    Description: attrs.Description || '',
    FillBackgroundColor: null,
    Opacity: 0,
    Tag: JSON.stringify(tag),
  };
};
const localRingToReading = (item: any) => {
  const { attrs } = item;
  const { pixelCoords = {} } = attrs;
  const { imagePoints = [] } = pixelCoords;
  const tag = {
    id: attrs.id,
    tag: item.tag,
    attrs: item.attrs,
    children: item.children,
  };
  return {
    Name: null,
    ID: generateId(Number(attrs.id)),
    Type: 'btn_ring',
    PartOfGroup: attrs.group,
    Coordinates: [],
    LineColor: convertColorToHex(attrs.stroke),
    LineWidth: attrs.strokeWidth,
    FontSize: 12,
    FontColor: '#FF000000',
    Area: 0,
    Description: attrs.Description || '',
    FillBackgroundColor: null,
    Opacity: 0,
    Tag: JSON.stringify(tag),
  };
};

const handleImagePoints = (imagePoints: number[]) => {
  return imagePoints.reduce(
    (acc: { X: number; Y: number }[], value: any, index: number, array: { [x: string]: any }) => {
      if (index % 2 === 0) {
        acc.push({ X: formatNumber(value), Y: formatNumber(array[index + 1]) });
      }
      return acc;
    },
    []
  );
};

const formatNumber = (value: number): number => {
  return Number.isInteger(value) ? value + 0.000000001 : value;
};

/**
 * 将阅片软件JSON数据转为切片库可用的数据
 */
export const RadingSoftwareJSONTolocalJSON = (viewer: OpenSeadragon.Viewer, readingData: any) => {
  const result = flatGroupModal(readingData.GroupModel, []);
  return result
    .map((item: any) => {
      const pointsInfo = handleReadingCoords(viewer, item);
      item.pointsInfo = pointsInfo;
      if (item.Type === 'btn_rect') {
        return readingRectToLocal(item);
      } else if (item.Type === 'btn_elips') {
        return readingElipsToLocal(item);
      } else if (item.Type === 'btn_arrow') {
        return readingArrowToLocal(item);
      } else if (item.Type === 'btn_brush') {
        // 自由曲线 和 闭合曲线
        return readingCurveToLocal(item);
      } else if (item.Type === 'btn_pen') {
        // 折线 和 多边形
        return readingFoldlineOrPolygonToLocal(item);
      } else if (item.Type === 'btn_point') {
        return readingElipsToLocal(item);
      } else if (item.Type === 'btn_angle') {
        return readingAngleToLocal(item);
      } else if (item.Type === 'btn_ring') {
        return readingRingToLocal(item);
      } else if (item.Type === 'btn_flag') {
        return readingFlagToLocal(item);
      } else {
        return {};
      }
    })
    .filter((item: any) => item.name);
};

const readingRectToLocal = (item: any) => {
  const id = createUUID();
  const { pointsInfo, Tag } = item;
  const tag = JSON.parse(Tag || '{}');
  const { attrs } = tag;
  return {
    name: 'rect',
    id,
    attrs: {
      ...attrs,
      tag: tag.tag,
      x: pointsInfo.leftTopPoint.x || attrs.x,
      y: pointsInfo.leftTopPoint.y || attrs.Y,
      id,
      name: 'rect',
      width: pointsInfo.rectWidth || attrs.width,
      height: pointsInfo.rectHeight || attrs.height,
      imagePoints: pointsInfo.imagePoints,
      pixelCoords: {
        x: pointsInfo.leftTopPoint.imageX,
        y: pointsInfo.leftTopPoint.imageY,
        imagePoints: pointsInfo.imagePoints,
      },
      draggable: false,
      strokeScaleEnabled: false,
      stroke: handleReadingcolor(item.LineColor),
      strokeWidth: item.LineWidth,
      dash: [],
    },
    node: '',
    index: item.ID,
    children: [],
    cName: '矩形',
    additionalInformation: '',
  };
};

const readingElipsToLocal = (item: any) => {
  const id = createUUID();
  const { pointsInfo, Tag } = item;
  const tag = JSON.parse(Tag || '{}');
  let isCircle = false;
  if (typeof tag.isCricle === 'boolean') {
    isCircle = tag.isCricle;
  }
  return {
    name: isCircle ? 'circle' : 'ellipse',
    id,
    attrs: {
      ...tag.attrs,
      tag: tag.tag,
      x: pointsInfo.rectCenterX,
      y: pointsInfo.rectCenterY,
      radiusX: pointsInfo.rectWidth,
      radiusY: pointsInfo.rectWidth,
      id,
      name: isCircle ? 'circle' : 'ellipse',
      imagePoints: pointsInfo.imagePoints,
      width: pointsInfo.rectWidth,
      height: pointsInfo.rectHeight,
      pixelCoords: {
        x: pointsInfo.rectImageCenterX,
        y: pointsInfo.rectImageCenterY,
        centerX: pointsInfo.rectImageCenterX,
        centerY: pointsInfo.rectImageCenterY,
        radiusX: Math.abs(pointsInfo.leftTopPoint.imageX - pointsInfo.rightBottonPoint.imageX),
        radiusY: Math.abs(pointsInfo.leftTopPoint.imageY - pointsInfo.rightBottonPoint.imageY),
        imagePoints: pointsInfo.imagePoints,
      },
      draggable: false,
      strokeScaleEnabled: false,
      stroke: handleReadingcolor(item.LineColor),
      strokeWidth: item.LineWidth,
      dash: [],
    },
    node: '',
    index: item.ID,
    children: [],
    cName: isCircle ? '圆形' : '椭圆',
    additionalInformation: '',
  };
};

const readingArrowToLocal = (item: any) => {
  const id = createUUID();
  const { pointsInfo, Tag } = item;
  const tag = JSON.parse(Tag || '{}');
  return {
    name: 'arrow',
    id,
    attrs: {
      ...tag.attrs,
      tag: tag.tag,
      x: pointsInfo.points[0].x,
      y: pointsInfo.points[0].y,
      id,
      name: 'arrow',
      imagePoints: pointsInfo.imagePoints,
      points: [
        0,
        0,
        pointsInfo.points[1].x - pointsInfo.points[0].x,
        pointsInfo.points[1].y - pointsInfo.points[0].y,
      ],
      pixelCoords: {
        x: pointsInfo.imagePoints[0],
        y: pointsInfo.imagePoints[1],
        imagePoints: pointsInfo.imagePoints,
      },
      draggable: false,
      strokeScaleEnabled: false,
      stroke: handleReadingcolor(item.LineColor),
      fill: handleReadingcolor(item.LineColor),
      strokeWidth: item.LineWidth,
      dash: [],
      hitStrokeWidth: 30,
    },
    node: '',
    index: item.ID,
    children: [],
    cName: '箭头',
    additionalInformation: '',
  };
};

const readingCurveToLocal = (item: any) => {
  const id = createUUID();
  const { pointsInfo, Tag } = item;
  const tag = JSON.parse(Tag || '{}');
  const isClosed = pointsInfo.isClosed;
  const name = isClosed ? 'closedCurve' : 'freeCurve';
  const cName = isClosed ? '闭合曲线' : '自由曲线';
  return {
    name: name,
    id,
    attrs: {
      ...tag.attrs,
      tag: tag.tag,
      id,
      name: name,
      imagePoints: pointsInfo.imagePoints,
      points: pointsInfo.pointsList,
      pixelCoords: {
        x: pointsInfo.imagePoints[0],
        y: pointsInfo.imagePoints[1],
        imagePoints: pointsInfo.imagePoints,
      },
      draggable: false,
      strokeScaleEnabled: false,
      stroke: handleReadingcolor(item.LineColor),
      strokeWidth: item.LineWidth,
      dash: [],
      hitStrokeWidth: 30,
      lineCap: 'round',
      lineJoin: 'round',
      closed: isClosed,
    },
    node: '',
    index: item.ID,
    children: [],
    cName: cName,
    additionalInformation: '',
  };
};

const readingFoldlineOrPolygonToLocal = (item: any) => {
  const id = createUUID();
  const { pointsInfo, Tag } = item;
  const tag = JSON.parse(Tag || '{}');
  const isClosed = pointsInfo.isClosed;
  let name = isClosed ? 'polygon' : 'foldLine';
  let cName = isClosed ? '多边形' : '折线';
  if (tag.name) {
    name = tag.name;
  }
  if (tag.cName) {
    cName = tag.cName;
  }
  return {
    name: name,
    id,
    attrs: {
      ...tag.attrs,
      tag: tag.tag,
      id,
      name: name,
      imagePoints: pointsInfo.imagePoints,
      points: pointsInfo.pointsList,
      pixelCoords: {
        x: pointsInfo.imagePoints[0],
        y: pointsInfo.imagePoints[1],
        imagePoints: pointsInfo.imagePoints,
      },
      draggable: false,
      strokeScaleEnabled: false,
      stroke: handleReadingcolor(item.LineColor),
      strokeWidth: item.LineWidth,
      dash: [],
      hitStrokeWidth: 30,
      lineCap: 'round',
      lineJoin: 'round',
      closed: isClosed,
    },
    node: '',
    index: item.ID,
    children: [],
    cName: cName,
    additionalInformation: '',
  };
};

const readingAngleToLocal = (item: any) => {
  const id = createUUID();
  const { pointsInfo, Tag } = item;
  const tag = JSON.parse(Tag || '{}');
  const name = 'angle';
  return {
    name: name,
    id,
    attrs: {
      ...tag.attrs,
      tag: tag.tag,
      id,
      name: name,
      imagePoints: pointsInfo.imagePoints,
      points: pointsInfo.pointsList,
      pixelCoords: {
        x: pointsInfo.imagePoints[0],
        y: pointsInfo.imagePoints[1],
        imagePoints: pointsInfo.imagePoints,
      },
      draggable: false,
      strokeScaleEnabled: false,
      stroke: handleReadingcolor(item.LineColor),
      strokeWidth: item.LineWidth,
      dash: [],
      hitStrokeWidth: 30,
      lineCap: 'round',
      lineJoin: 'round',
      closed: false,
      ...tag?.attrs,
    },
    node: '',
    index: item.ID,
    children: [],
    cName: '夹角',
    additionalInformation: '',
  };
};

const readingRingToLocal = (item: any) => {
  const id = createUUID();
  const name = 'ring';
  const { Tag } = item;
  const tag = JSON.parse(Tag || '{}');

  return {
    name: name,
    id,
    attrs: {
      ...tag?.attrs,
      tag: tag.tag,
      id,
      name: name,
      dash: [],
    },
    node: '',
    index: item.ID,
    children: tag.children || [],
    cName: '同心圆',
    additionalInformation: '',
  };
};
const readingFlagToLocal = (item: any) => {
  const id = createUUID();
  const name = 'flag';
  const { Tag } = item;
  const tag = JSON.parse(Tag || '{}');

  return {
    name: name,
    id,
    attrs: {
      ...tag?.attrs,
      tag: tag.tag,
      id,
      name: name,
      dash: [],
    },
    node: '',
    index: item.ID,
    cName: '标旗',
    additionalInformation: '',
  };
};

const handleReadingPoints = (points: number[]) => {
  return points.reduce((acc: any, cur: any) => {
    acc.push(cur.x || cur.X);
    acc.push(cur.y || cur.Y);
    return acc;
  }, []);
};

const flatGroupModal = (GroupModel: any, result: any[] = []): any => {
  let flatGroupResult: any[] = [];

  if (GroupModel.Name === 'root' && GroupModel.Labels) {
    flatGroupResult = [
      ...flatGroupResult,
      ...GroupModel.Labels.map((item: any) => ({ ...item, GroupName: GroupModel.Name })),
    ];
  }

  const loop = (Groups: any) => {
    if (Groups?.length > 0) {
      for (let i = 0; i < Groups.length; i++) {
        const group = Groups[i];
        flatGroupResult = [
          ...flatGroupResult,
          ...group.Labels.map((item: any) => ({ ...item, GroupName: group.Name })),
        ];
        if (group.Groups?.length > 0) {
          loop(group.Groups);
        }
      }
    }
  };

  loop(GroupModel.Groups);
  // console.log(GroupModel, ' GroupModel');
  // if (GroupModel.Groups?.length > 0) {
  //   for (let i = 0; i < GroupModel.Groups.length; i++) {
  //     const group = GroupModel.Groups[i];
  //     result = [
  //       ...result,
  //       ...group.Labels.map((item: any) => ({ ...item, GroupName: group.Name })),
  //     ];
  //     return flatGroupModal(group, result);
  //   }
  // }
  return flatGroupResult;
};

const createUUID = (): string => {
  // return new Date().getTime();
  return uuidv4();
};
// 阅片软件的颜色十六进制 是rgba 要转为rgba的顺序
const handleReadingcolor = (hexString: string): string => {
  if (hexString.length === 9) {
    const alpha = hexString.slice(1, 3); // '12'
    return `#${hexString.slice(3)}${alpha}`;
  } else {
    return hexString;
  }
};

// 处理阅片软件的coords 抛出需要的数据
const handleReadingCoords = (viewer: OpenSeadragon.Viewer, item: any) => {
  try {
    const coords = item.Coordinates;
    const points = coords
      .filter((item: any) => (item.X && item.Y) || (item.x && item.Y))
      .map((coor: any) => {
        const point = imageCoordinatesToViewCoordinates(viewer, coor.X, coor.Y);
        return {
          x: point?.x,
          y: point?.y,
        };
      });

    let leftTopPoint = undefined;
    let rightBottonPoint = undefined;
    if (coords?.length <= 0 || points?.length <= 0) {
      leftTopPoint = {
        x: 0,
        y: 0,
        imageX: 0,
        imageY: 0,
      };
      rightBottonPoint = {
        x: 0,
        y: 0,
        imageX: 0,
        imageY: 0,
      };
    } else if (points[0].x < points[1].x && points[0].y < points[1].y) {
      leftTopPoint = {
        x: points[0].x,
        y: points[0].y,
        imageX: coords[0].x || coords[0].X,
        imageY: coords[0].y || coords[0].Y,
      };
      rightBottonPoint = {
        x: points[1].x,
        y: points[1].y,
        imageX: coords[1].x || coords[1].X,
        imageY: coords[1].y || coords[1].Y,
      };
    } else {
      leftTopPoint = {
        x: points[1].x,
        y: points[1].y,
        imageX: coords[1].x || coords[1].X,
        imageY: coords[1].y || coords[1].Y,
      };
      rightBottonPoint = {
        x: points[0].x,
        y: points[0].y,
        imageX: coords[0].x || coords[0].X,
        imageY: coords[0].y || coords[0].Y,
      };
    }
    const rectWidth = Math.abs(rightBottonPoint.x - leftTopPoint.x);
    const rectHeight = Math.abs(rightBottonPoint.y - leftTopPoint.y);
    const rectCenterX = (rightBottonPoint.x + leftTopPoint.x) / 2;
    const rectCenterY = (rightBottonPoint.y + leftTopPoint.y) / 2;
    const rectImageWidth = Math.abs(rightBottonPoint.imageX - leftTopPoint.imageX);
    const rectImageHeight = Math.abs(rightBottonPoint.imageY - leftTopPoint.imageY);
    const rectImageCenterX = (rightBottonPoint.imageX + leftTopPoint.imageX) / 2;
    const rectImageCenterY = (rightBottonPoint.imageY + leftTopPoint.imageY) / 2;
    const imagePoints = handleReadingPoints(coords);
    const pointsList = handleReadingPoints(points);

    const firstPoint = coords[0] || { X: 0, Y: 0 };
    const lastPoint = coords[coords.length - 1] || { X: 0, Y: 0 };
    const isClosed = firstPoint.X === lastPoint.X && firstPoint.Y === lastPoint.Y;

    return {
      leftTopPoint,
      rightBottonPoint,
      rectWidth,
      rectHeight,
      rectCenterX,
      rectCenterY,
      rectImageWidth,
      rectImageHeight,
      rectImageCenterX,
      rectImageCenterY,
      imagePoints,
      points,
      pointsList,
      isClosed,
    };
  } catch (error) {
    console.log(error, 'handleReadingCoords-error');
  }
};
