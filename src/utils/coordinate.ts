// 坐标转换相关的方法
import OpenSeadragon, { Point } from 'openseadragon';

export interface RectOptions {
  x: number;
  y: number;
  width: number;
  height: number;
}

// 将视图坐标转换为图像坐标
export const viewCoordinatesToImageCoordinates = (
  viewer: OpenSeadragon.Viewer,
  x: number,
  y: number
) => {
  const viewportPoint = viewer.viewport.pointFromPixel(new Point(x, y));

  let imagePoint = null;
  if (process.env.NODE_ENV === 'development') {
    imagePoint = viewer.viewport.viewportToImageCoordinates(viewportPoint);
  } else {
    imagePoint = viewer.world
      .getItemAt(0)
      .viewportToImageCoordinates(viewportPoint.x, viewportPoint.y);
  }
  // 左上坐标
  // const pixelNoRotate = viewer.viewport.pixelFromPointNoRotate(new OpenSeadragon.Point(0, 0), true);
  // const point = viewer.viewport.pointFromPixel(new Point(x, y));
  // const pixel = viewer.viewport.pixelFromPointNoRotate(point); //当前坐标转化为旋转度数为0度的实际坐标
  return {
    // x: (pixel.x - pixelNoRotate.x) / this.layerScale,
    // y: (pixel.y - pixelNoRotate.y) / this.layerScale,
    imageX: imagePoint.x,
    imageY: imagePoint.y,
    // dx: x,
    // dy: y,
  };
};

/**
 * 根据传入的矩阵x, y, width, height 求出矩阵4个点的图像坐标
 *
 */
export const getRectImagePoint = (viewer: OpenSeadragon.Viewer, rectOptions: RectOptions) => {
  const { x, y, width, height } = rectOptions;
  return [
    viewCoordinatesToImageCoordinates(viewer, x, y),
    viewCoordinatesToImageCoordinates(viewer, x + width, y),
    viewCoordinatesToImageCoordinates(viewer, x + width, y + height),
    viewCoordinatesToImageCoordinates(viewer, x, y + height),
  ];
};

export const imageCoordinatesToViewCoordinates = (
  viewer: OpenSeadragon.Viewer,
  x: number,
  y: number
) => {
  const imageItem = viewer.world.getItemAt(0);
  let ps = null;
  if (imageItem) {
    const pt = new OpenSeadragon.Point(Number(x), Number(y));
    ps = imageItem.imageToViewportCoordinates(pt.x, pt.y, true);
  } else {
    // 之前用的是这个方法 线上环境存在问题
    ps = viewer.viewport.imageToViewportCoordinates(new OpenSeadragon.Point(Number(x), Number(y)));
  }
  const ratio = process.env.NODE_ENV === 'development' ? 150 : 1000;
  // const result = viewer.viewport.viewportToViewerElementCoordinates(ps)
  // const ps = viewer.viewport.imageToViewportCoordinates(x, y);
  return {
    // x: result.x,
    // y: result.y,
    x: ps.x * ratio,
    y: ps.y * ratio,
  };
};
