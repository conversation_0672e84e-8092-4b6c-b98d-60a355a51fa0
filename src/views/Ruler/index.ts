import OpenSeadragon from 'openseadragon';
interface PicHead {
  /** srcWidth*/
  srcWidth: number;
  /** rate*/
  rate: number;
  /** ruler*/
  ruler: number;
}
export default class RulerOverlay {
  protected _viewer: OpenSeadragon.Viewer;
  protected _picHead: PicHead;
  protected _canvasDiv: HTMLDivElement;
  protected _containerWidth: number = 0;
  protected _containerHeight: number = 0;
  private _interval: { interval: number; intervalValue: number };
  private showGridLines: boolean = false;
  constructor(viewer: OpenSeadragon.Viewer, picHead: PicHead) {
    this._viewer = viewer;
    this._picHead = picHead;
    this._containerWidth = this._viewer.container.clientWidth;
    this._containerHeight = this._viewer.container.clientHeight;
    const existingCanvasDiv = this._viewer.canvas.querySelector('.ruler-overlay');
    if (existingCanvasDiv) {
      this._canvasDiv = existingCanvasDiv as HTMLDivElement;
    } else {
      this.createCanvasDiv();
      this.addEventListeners();
    }
  }

  // 控制网格线显示或隐藏的方法
  toggleGridLines(show: boolean) {
    this.showGridLines = show;
    this.updateRulers();
  }

  // 创建包含比例尺的 div
  createCanvasDiv() {
    this._canvasDiv = document.createElement('div');
    this._canvasDiv.classList.add('ruler-overlay');
    this._canvasDiv.style.position = 'absolute';
    this._canvasDiv.style.left = '0';
    this._canvasDiv.style.top = '0';
    this._canvasDiv.style.width = '100%';
    this._canvasDiv.style.height = '100%';
    this._canvasDiv.style.zIndex = '101';
    this._viewer.canvas.appendChild(this._canvasDiv);
    this.handleZoom();
    this.updateRulers();
  }

  // 添加事件监听器
  addEventListeners() {
    this._viewer.addHandler('update-viewport', () => {
      this.updateRulers();
      this._containerWidth = this._viewer.container.clientWidth;
      this._containerHeight = this._viewer.container.clientHeight;
    });

    this._viewer.addHandler('zoom', () => {
      this.handleZoom();
    });
  }
  handleZoom() {
    const zoomValue = this._viewer.viewport.getZoom();
    const zoom =
      this._viewer.viewport.viewportToImageZoom(
        zoomValue / this.setRegistration3D(this._picHead.ruler, this._picHead.rate)
      ) * this._picHead.rate;
    const interval = this.determineInterval(zoom);
    this.calculateInterval(interval);
  }
  determineInterval(zoom: number): number {
    if (zoom < 1) return 3200;
    if (zoom <= 1.9) return 800;
    if (zoom <= 3.9) return 400;
    if (zoom <= 9.9) return 200;
    if (zoom <= 19.9) return 100;
    if (zoom <= 39.9) return 50;
    return 25;
  }
  //根据3d系数进行缩放转换
  setRegistration3D(ruler: number, rate: number) {
    const Mpp3D = 0.242797; // 分辨率
    const Rate3D = 20; //扫描倍率
    const Scale3D = 0.242797 / 0.5; //分辨率 0.242797um/pixel, 100pixels = 50um 映射比例
    return (ruler / Mpp3D) * Scale3D * (rate / Rate3D);
  }
  // 计算刻度间隔
  calculateInterval(intervalValue: number) {
    const containerWidth = this._viewer.container.clientWidth;
    const transformedWidth =
      (this._picHead.srcWidth / (containerWidth * this._viewer.viewport.getZoom())) *
      this._picHead.ruler;
    this._interval = {
      interval: intervalValue / transformedWidth,
      intervalValue,
    };
  }

  // 获取图像左上角的偏移位置
  calculateImageTopLeftOffset() {
    return this._viewer.viewport.viewportToViewerElementCoordinates(new OpenSeadragon.Point(0, 0));
  }

  // 更新刻度尺位置和标记
  updateRulers() {
    this._canvasDiv.innerHTML = ''; // 清除原有内容
    const offset = this.calculateImageTopLeftOffset();
    const subInterval = this._interval.interval / 5;
    const textPadding = 5;

    // 绘制上方X轴刻度尺
    this.drawXAxisRuler(offset, subInterval, textPadding);

    // 绘制左侧Y轴刻度尺
    this.drawYAxisRuler(offset, subInterval, textPadding);
  }

  // 绘制X轴刻度
  drawXAxisRuler(offset: { x: number; y: number }, subInterval: number, textPadding: number) {
    let xPos = offset.x % this._interval.interval;
    if (xPos < 0) xPos += this._interval.interval;
    let xValue =
      Math.floor(-offset.x / this._interval.interval) * this._interval.interval +
      this._interval.interval;

    // 创建白色背景矩形
    const rulerBackground = document.createElement('div');
    rulerBackground.style.position = 'absolute';
    rulerBackground.style.left = '0';
    rulerBackground.style.top = '0';
    rulerBackground.style.width = `${this._containerWidth}px`;
    rulerBackground.style.height = '20px';
    rulerBackground.style.boxSizing = 'border-box';
    rulerBackground.style.backgroundColor = 'var(--color-bg-1)';
    rulerBackground.style.borderBottom = '1px solid var(--color-text-1)';
    rulerBackground.style.borderTop = '1px solid var(--color-text-1)';
    rulerBackground.style.zIndex = '1';
    this._canvasDiv.appendChild(rulerBackground);

    while (xPos < this._containerWidth) {
      const mappedXValue = this.mapValue(
        xValue,
        this._interval.interval,
        this._interval.intervalValue
      );

      // 绘制主刻度线
      const mainLine = document.createElement('div');
      mainLine.style.position = 'absolute';
      mainLine.style.left = `${xPos}px`;
      mainLine.style.top = '0';
      mainLine.style.width = '1px';
      mainLine.style.height = '20px';
      mainLine.style.backgroundColor = 'var(--color-text-1)';
      mainLine.style.zIndex = '2';
      mainLine.style.transition = 'all 0.3s ease';
      this._canvasDiv.appendChild(mainLine);

      // 绘制刻度值文本
      const text = document.createElement('div');
      text.style.position = 'absolute';
      text.style.left = `${xPos + textPadding}px`;
      text.style.top = '1px';
      text.style.fontSize = '12px';
      text.style.color = 'var(--color-text-1)';
      text.innerText = mappedXValue.toFixed();
      text.style.zIndex = '2';
      text.style.userSelect = 'none';
      text.style.transition = 'all 0.3s ease';
      this._canvasDiv.appendChild(text);

      // 绘制网格线，仅当 showGridLines 为 true 时
      if (this.showGridLines) {
        const gridLine = document.createElement('div');
        gridLine.style.position = 'absolute';
        gridLine.style.left = `${xPos}px`;
        gridLine.style.top = '0';
        gridLine.style.width = '1px';
        gridLine.style.height = `${this._containerHeight}px`;
        gridLine.style.backgroundColor = 'var(--color-text-1)';
        gridLine.style.zIndex = '0';
        gridLine.style.transition = 'all 0.3s ease';
        this._canvasDiv.appendChild(gridLine);
      }

      // 绘制子刻度线
      for (let i = 1; i < 5; i++) {
        const subXPos = xPos + subInterval * i;
        const subLine = document.createElement('div');
        subLine.style.position = 'absolute';
        subLine.style.left = `${subXPos}px`;
        subLine.style.top = '15px';
        subLine.style.width = '1px';
        subLine.style.height = '5px';
        subLine.style.backgroundColor = 'var(--color-text-1)';
        subLine.style.zIndex = '2';
        subLine.style.transition = 'all 0.3s ease';
        this._canvasDiv.appendChild(subLine);
      }

      xPos += this._interval.interval;
      xValue += this._interval.interval;
    }
  }

  // 绘制Y轴刻度
  drawYAxisRuler(offset: { x: number; y: number }, subInterval: number, textPadding: number) {
    let yPos = offset.y % this._interval.interval;
    if (yPos <= 0) yPos += this._interval.interval;
    let yValue =
      Math.floor(-offset.y / this._interval.interval) * this._interval.interval +
      this._interval.interval;

    // 创建白色背景矩形
    const rulerBackground = document.createElement('div');
    rulerBackground.style.position = 'absolute';
    rulerBackground.style.left = '0';
    rulerBackground.style.top = '0';
    rulerBackground.style.width = '20px';
    rulerBackground.style.height = `${this._containerHeight}px`;
    rulerBackground.style.boxSizing = 'border-box';
    rulerBackground.style.backgroundColor = 'var(--color-bg-1)';
    rulerBackground.style.borderRight = '1px solid var(--color-text-1)';
    rulerBackground.style.borderLeft = '1px solid var(--color-text-1)';
    rulerBackground.style.zIndex = '2';
    this._canvasDiv.appendChild(rulerBackground);

    while (yPos < this._containerHeight) {
      const mappedYValue = this.mapValue(
        yValue,
        this._interval.interval,
        this._interval.intervalValue
      );

      // 绘制主刻度线
      const mainLine = document.createElement('div');
      mainLine.style.position = 'absolute';
      mainLine.style.left = '0';
      mainLine.style.top = `${yPos}px`;
      mainLine.style.width = '20px';
      mainLine.style.height = '1px';
      mainLine.style.backgroundColor = 'var(--color-text-1)';
      mainLine.style.zIndex = '3';
      mainLine.style.transition = 'all 0.3s ease';
      this._canvasDiv.appendChild(mainLine);

      // 绘制刻度值文本
      const text = document.createElement('div');
      text.style.position = 'absolute';
      text.style.left = '16px';
      text.style.top = `${yPos + textPadding}px`;
      text.style.fontSize = '12px';
      text.style.color = 'var(--color-text-1)';
      text.style.userSelect = 'none';
      text.innerText = mappedYValue.toFixed();
      // 使文本垂直显示
      text.style.transform = 'rotate(90deg)';
      text.style.transformOrigin = 'top left';
      text.style.whiteSpace = 'nowrap';
      text.style.zIndex = '3';
      text.style.transition = 'all 0.3s ease';
      this._canvasDiv.appendChild(text);

      // 绘制网格线，仅当 showGridLines 为 true 时
      if (this.showGridLines) {
        const gridLine = document.createElement('div');
        gridLine.style.position = 'absolute';
        gridLine.style.left = '0';
        gridLine.style.top = `${yPos}px`;
        gridLine.style.width = `${this._containerWidth}px`;
        gridLine.style.height = '1px';
        gridLine.style.backgroundColor = 'var(--color-text-1)';
        gridLine.style.transition = 'all 0.3s ease';
        this._canvasDiv.appendChild(gridLine);
      }

      // 绘制子刻度线
      for (let i = 1; i < 5; i++) {
        const subYPos = yPos + subInterval * i;
        const subLine = document.createElement('div');
        subLine.style.position = 'absolute';
        subLine.style.left = '15px';
        subLine.style.top = `${subYPos}px`;
        subLine.style.width = '5px';
        subLine.style.height = '1px';
        subLine.style.backgroundColor = 'var(--color-text-1)';
        subLine.style.zIndex = '3';
        subLine.style.transition = 'all 0.3s ease';
        this._canvasDiv.appendChild(subLine);
      }

      yPos += this._interval.interval;
      yValue += this._interval.interval;
    }
  }

  //映射数值至刻度
  mapValue(value: number, interval: number, intervalValue: number): number {
    return (value / interval) * intervalValue;
  }
  /**
   * 清除画布
   */
  clear() {
    if (this._canvasDiv && this._canvasDiv.parentNode) {
      this._canvasDiv.parentNode.removeChild(this._canvasDiv);
    }
  }
}
