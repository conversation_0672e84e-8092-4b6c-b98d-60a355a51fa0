import { BaseDrawer } from './BaseDrawer';
import Konva from 'konva';
import OpenSeadragon, { Point } from 'openseadragon';
import { Layer } from 'konva/lib/Layer';
import { DrawType, DrawStatus } from '@/views/Marker/components/editorEnum';
import { isMouseLeftOrTouch } from '@/utils/index';
import { Context } from 'konva/lib/Context';
export class EllipseDrawer extends BaseDrawer<Konva.Ellipse> {
  private _EllipseConfig: any;

  _ratio: any = {
    unit: 'px',
    scales: 1,
  };

  constructor(layer: Layer, viewer: OpenSeadragon.Viewer, ellipseConfig: any) {
    super(layer, viewer, ellipseConfig);
    this._EllipseConfig = ellipseConfig;
    console.log(this._EllipseConfig, 'lineconfig');
  }

  override drawDown() {
    this.start();
    this.startPoint = this.curPoint;
    this.node = this.createNode();
    this.layer.add(this.node);
    this.setNodeId();
  }

  override drawMove() {
    const { x, y, width, height } = this.getRect(this.startPoint, this.curPoint);

    const radiusX = width / 2;
    const radiusY = height / 2;
    const centerX = x + width / 2;
    const centerY = y + height / 2;
    const curPoint = this.getCurPoint();
    const leftTopPoint = this.viewCoordinatesToImageCoordinates(this.viewer, x, y);
    const centerPoint = this.viewCoordinatesToImageCoordinates(this.viewer, centerX, centerY);
    this.node?.setAttrs({
      x: centerX,
      y: centerY,
      radiusX,
      radiusY,
      pixelCoords: {
        x: leftTopPoint.imageX,
        y: leftTopPoint.imageY,
        centerX: centerPoint.imageX,
        centerY: centerPoint.imageY,
        radiusX: Math.abs(curPoint.imageX - this.startPoint.imageX),
        radiusY: Math.abs(curPoint.imageY - this.startPoint.imageY),
        imagePoints: [
          this.startPoint.imageX,
          this.startPoint.imageY,
          curPoint.imageX,
          curPoint.imageY,
        ],
      },
    });
  }

  override drawUp() {
    if (isMouseLeftOrTouch(this._EllipseConfig.ev)) {
      const { width, height } = this.getRect(this.startPoint, this.curPoint);
      // 鼠标左键
      if (width < this.minWidth || height < this.minHeight) {
        this.nodeDestroy();
      }
      this.status = DrawStatus.drawingCompleted;
      this.end();
    }
  }

  // 创建node 抽离方便后续addmarker调用
  createNode() {
    const { x, y, width, height } = this.getRect(this.startPoint, this.curPoint);
    const radiusX = width / 2;
    const radiusY = height / 2;
    const centerX = x + width / 2;
    const centerY = y + height / 2;
    const node = new Konva.Ellipse({
      ...this._EllipseConfig?.config,
      x: centerX,
      y: centerY,
      radiusX,
      radiusY,
      name: DrawType.ellipse,
      strokeScaleEnabled: false,
      zoom: this.viewer.viewport.getZoom(),
    });
    node.on('dragmove dragend transform transformend', (e: any) => {
      const { x, y, width, height } = node.getClientRect();
      const startPoint = this.viewCoordinatesToImageCoordinates(this.viewer, x, y);
      const curPoint = this.viewCoordinatesToImageCoordinates(this.viewer, x + width, y + height);
      const imageRadiusX = (curPoint.imageX - startPoint.imageX) / 2;
      const imageRadiusY = (curPoint.imageY - startPoint.imageY) / 2;
      const imageCenterX = startPoint.imageX + imageRadiusX;
      const imageCenterY = startPoint.imageY + imageRadiusY;
      node.setAttrs({
        pixelCoords: {
          x: startPoint.imageX,
          y: startPoint.imageY,
          centerX: imageCenterX,
          centerY: imageCenterY,
          radiusX: imageRadiusX,
          radiusY: imageRadiusY,
          imagePoints: [startPoint.imageX, startPoint.imageY, curPoint.imageX, curPoint.imageY],
        },
      });
    });
    if (this._EllipseConfig.needCountArea) {
      node.setAttr('sceneFunc', (con: Context, shape: any) => {
        con.beginPath();
        con.lineWidth = 1 / this.layerScale;
        con.strokeStyle = shape.attrs.stroke;
        const { radiusX, radiusY, pixelCoords } = node.attrs;
        con.ellipse(0, 0, radiusX, radiusY, Math.PI * 2, 0, Math.PI * 2);
        con.closePath();
        con.fillStrokeShape(shape as any);
        con.fillStyle = shape.attrs.stroke;
        con.scale(
          Math.min(1, 2 / this.layerScale) / shape.scaleX(),
          Math.min(1, 2 / this.layerScale) / shape.scaleY()
        );
        if (this.selectNode?.attrs?.id === node.attrs?.id || this.status !== DrawStatus.end) {
          if (!pixelCoords?.imagePoints) return;
          const Description = this.ellipseAreaPerimeter(
            pixelCoords?.imagePoints[2] - pixelCoords?.imagePoints[0],
            pixelCoords?.imagePoints[3] - pixelCoords?.imagePoints[1]
          );
          con.fillText(
            Description,
            -((radiusX / 2) * shape.scaleX()),
            -((radiusY / Math.min(1, 2 / this.layerScale)) * shape.scaleY())
          );
          // if(this.status === DrawStatus.select) {
          node?.setAttrs({
            Description,
          });
          // }
        } else {
          con.fillText(
            shape.attrs.Description || '',
            -((radiusX / 2) * shape.scaleX()),
            -((radiusY / Math.min(1, 2 / this.layerScale)) * shape.scaleY())
          );
        }
      });
    }
    return node;
  }

  /**
   * 计算椭圆形面积
   */
  ellipseAreaPerimeter: (x: number, y: number) => string = (x, y) => {
    const w = (x * this._ratio.scales) / 2;
    const h = (y * this._ratio.scales) / 2;
    return (
      (Math.PI * w * h).toFixed(2) +
      this._ratio.unit +
      '² ' +
      (2 * Math.PI * Math.min(w, h) + 4 * Math.abs(w - h)).toFixed(2) +
      this._ratio.unit
    );
  };
}
