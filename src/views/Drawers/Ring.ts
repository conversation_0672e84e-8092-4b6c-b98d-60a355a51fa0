import { BaseDrawer } from './BaseDrawer';
import Konva from 'konva';
import OpenSeadragon, { Point } from 'openseadragon';
import { Layer } from 'konva/lib/Layer';
import { DrawType, DrawStatus } from '@/views/Marker/components/editorEnum';
import { isMouseLeftOrTouch } from '@/utils/index';
export class RingDrawer extends BaseDrawer<Konva.Line | Konva.Group> {
  private _ringCircle: any;
  private _ring: Konva.Ring;
  private _centerCircle: Konva.Circle;
  private _innerCircle: Konva.Circle;
  private _outerCircle: Konva.Circle;
  private _initPoint: any = {};
  constructor(layer: Layer, viewer: OpenSeadragon.Viewer, lineConfig: any) {
    super(layer, viewer, lineConfig);
    this._ringCircle = lineConfig;
  }

  // 求两点的直线距离
  private _getDistance(startPoint: any, endPoint: any) {
    const { x: x1, y: y1 } = startPoint;
    const { x: x2, y: y2 } = endPoint;
    const result = ((x1 - x2) ** 2 + (y1 - y2) ** 2) ** 0.5;
    return result;
  }

  override drawDown() {
    if (!this.node) {
      this.start();
      this.startPoint = this.curPoint;
      this._initPoint = this.curPoint;
      this.node = this.createNode();
      this.layer.add(this.node);
      this.setNodeId();
    }
  }

  override drawMove() {
    const radius = this._getDistance(this.startPoint, this.curPoint);
    this._ring?.setAttrs({
      innerRadius: radius / 2,
      outerRadius: radius,
    });
    const anchorRadius = radius / 12 > 1 ? 1 : radius / 12;
    this._centerCircle.setAttrs({
      radius: anchorRadius,
    });
    this._innerCircle.setAttrs({
      x: this.startPoint.x + (this.curPoint.x - this.startPoint.x) / 2,
      y: this.startPoint.y + (this.curPoint.y - this.startPoint.y) / 2,
      radius: anchorRadius,
    });
    this._outerCircle.setAttrs({
      x: this.curPoint.x,
      y: this.curPoint.y,
      radius: anchorRadius,
    });
  }

  override drawUp() {
    if (isMouseLeftOrTouch(this._ringCircle.ev)) {
      const { width, height } = this.getRect(this.startPoint, this.curPoint);
      // 鼠标左键
      if (width < this.minWidth || height < this.minHeight) {
        this.nodeDestroy();
      }
      // this._innerCircle.setAttrs({
      //   x: this.curPoint.x,
      //   y: this.curPoint.y,
      //   radius: 1,
      // });
      this._ring.setAttr('dash', []);
      this.status = DrawStatus.drawingCompleted;
      this.end();
    }
  }

  // 创建rect 抽离方便后续addmarker调用
  createNode() {
    const node = new Konva.Group({
      tName: 'Group_Ring',
      name: DrawType.ring,
      group: this._ringCircle?.config?.group,
      parentGroup: this._ringCircle?.config?.parentGroup,
    });
    this._ring = new Konva.Ring({
      ...this._ringCircle.config,
      hitStrokeWidth: 30,
      x: this.startPoint.x,
      y: this.startPoint.y,
      innerRadius: 10,
      outerRadius: 20,
      tName: 'Group_Ring_Ring',
      strokeScaleEnabled: false,
      zoom: this.viewer.viewport.getZoom(),
      group: null,
      parentGroup: null,
    });
    this._centerCircle = new Konva.Circle({
      x: this.startPoint.x,
      y: this.startPoint.y,
      radius: 1,
      fill: 'blue',
      draggable: true,
      tName: 'Group_Ring_Center_Anchor',
      visible: false,
    });
    this._innerCircle = new Konva.Circle({
      radius: 1,
      fill: 'blue',
      draggable: true,
      tName: 'Group_Ruler_InnerCircle_Anchor',
      visible: false,
      strokeScaleEnabled: false,
    });
    this._outerCircle = new Konva.Circle({
      radius: 1,
      fill: 'blue',
      draggable: true,
      tName: 'Group_Ruler_OuterCircle_Anchor',
      visible: false,
      strokeScaleEnabled: false,
    });
    this._centerCircle.on('dragmove', () => {
      // this._centerCircle.setAttrs({
      //   x: this.curPoint.x,
      //   y: this.curPoint.y,
      // });
      if (!this._initPoint.x) {
        this._initPoint = {
          x: this._centerCircle.attrs.x,
          y: this._centerCircle.attrs.y,
        };
      }
      node.setAttrs({
        x: this.curPoint.x - this._initPoint.x,
        y: this.curPoint.y - this._initPoint.y,
      });
    });
    this._innerCircle.on('dragmove', (ev) => {
      this._innerCircle.setAttrs({
        x: this.curPoint.x - (node.attrs.x || 0),
        y: this.curPoint.y - (node.attrs.y || 0),
      });
      const radius = this._getDistance(
        {
          x: this._centerCircle.attrs.x,
          y: this._centerCircle.attrs.y,
        },
        {
          x: this.curPoint.x - (node.attrs.x || 0),
          y: this.curPoint.y - (node.attrs.y || 0),
        }
      );
      this._ring.setAttrs({
        innerRadius: radius,
      });
    });
    this._outerCircle.on('dragmove', () => {
      this._outerCircle.setAttrs({
        x: this.curPoint.x - (node.attrs.x || 0),
        y: this.curPoint.y - (node.attrs.y || 0),
      });
      const radius = this._getDistance(
        {
          x: this._centerCircle.attrs.x,
          y: this._centerCircle.attrs.y,
        },
        {
          x: this.curPoint.x - (node.attrs.x || 0),
          y: this.curPoint.y - (node.attrs.y || 0),
        }
      );
      this._ring.setAttrs({
        outerRadius: radius,
      });
    });
    node.add(this._ring);
    node.add(this._centerCircle);
    node.add(this._innerCircle);
    node.add(this._outerCircle);
    return node;
  }
}
