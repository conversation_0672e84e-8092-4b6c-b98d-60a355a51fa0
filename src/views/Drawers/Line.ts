import { BaseDrawer } from './BaseDrawer';
import Konva from 'konva';
import { Line, LineConfig } from 'konva/lib/shapes/Line';
import OpenSeadragon, { Point } from 'openseadragon';
import { Layer } from 'konva/lib/Layer';
import { DrawType, DrawStatus } from '@/views/Marker/components/editorEnum';
import { isMouseLeftOrTouch } from '@/utils/index';
import { Context } from 'konva/lib/Context';
export class LineDrawer extends BaseDrawer<Konva.Line | Konva.Group> {
  private _lineConfig: any;
  constructor(layer: Layer, viewer: OpenSeadragon.Viewer, lineConfig: any) {
    super(layer, viewer, lineConfig);
    this._lineConfig = lineConfig;
  }

  _ratio: any = {
    unit: 'px',
    scales: 1,
  };
  override drawDown() {
    if (!this.node) {
      this.start();
      this.startPoint = this.curPoint;
      this.node = this.createNode();
      this.layer.add(this.node);
      this.setNodeId();
    }
  }

  override drawMove() {
    this.node?.setAttrs({
      points: [this.startPoint.x, this.startPoint.y, this.curPoint.x, this.curPoint.y],
      pixelCoords: {
        x: this.startPoint.imageX,
        y: this.startPoint.imageY,
        imagePoints: [
          this.startPoint.imageX,
          this.startPoint.imageY,
          this.curPoint.imageX,
          this.curPoint.imageY,
        ],
      },
    });
  }

  override drawUp() {
    if (isMouseLeftOrTouch(this._lineConfig.ev)) {
      const { width, height } = this.getRect(this.startPoint, this.curPoint);
      // 鼠标左键
      if (width < this.minWidth || height < this.minHeight) {
        this.nodeDestroy();
      }
      this.status = DrawStatus.drawingCompleted;
      this.end();
    }
  }

  // 创建rect 抽离方便后续addmarker调用
  createNode() {
    const node = new Konva.Line({
      ...this._lineConfig.config,
      points: [this.startPoint.x, this.startPoint.y],
      imagePoint: [this.startPoint, this.curPoint],
      hitStrokeWidth: 30,
      name: DrawType.line,
      strokeScaleEnabled: false,
      zoom: this.viewer.viewport.getZoom(),
    });
    node.on('dragmove dragend transform transformend', (e: any) => {
      const originalPoints = node.getAttrs().points;
      const transform = node.getAbsoluteTransform();
      const resultPoints = [];
      for (let i = 0; i < originalPoints.length; i += 2) {
        const x = originalPoints[i];
        const y = originalPoints[i + 1];
        const pt = transform.point({ x, y });
        const resultPoint = this.viewCoordinatesToImageCoordinates(this.viewer, pt.x, pt.y);
        resultPoints.push(resultPoint.imageX, resultPoint.imageY);
      }
      node?.setAttrs({
        pixelCoords: {
          x: resultPoints[0],
          y: resultPoints[1],
          imagePoints: resultPoints,
        },
      });
    });
    if (this._lineConfig.needCountArea) {
      node.setAttr('sceneFunc', (con: Context, shape: any) => {
        con.beginPath();
        for (let i = 0; i < shape.points().length; i += 2) {
          con.lineTo(shape.points()[i], shape.points()[i + 1]);
        }
        con.closePath();
        con.fillStrokeShape(shape as any);
        con.fillStyle = shape.attrs.stroke;
        con.scale(
          Math.min(1, 2 / this.layerScale) / shape.scaleX(),
          Math.min(1, 2 / this.layerScale) / shape.scaleY()
        );
        if (this.selectNode?.attrs?.id === node.attrs?.id || this.status !== DrawStatus.end) {
          if(!node.attrs.pixelCoords?.imagePoints) return
          let endPoint = {imageX: node.attrs.pixelCoords?.imagePoints[2], imageY: node.attrs.pixelCoords?.imagePoints[3]};
          let startpoint = {imageX: node.attrs.pixelCoords?.imagePoints[0], imageY: node.attrs.pixelCoords?.imagePoints[1]};
          const Description =
            this.pointPerimeter(
              {
                imageX: startpoint.imageX * (shape.attrs.scaleX || 1),
                imageY: startpoint.imageY * (shape.attrs.scaleY || 1),
              },
              {
                imageX: endPoint.imageX * (shape.attrs.scaleX || 1),
                imageY: endPoint.imageY * (shape.attrs.scaleY || 1),
              },
              this._ratio
            ) + this._ratio.unit;
          con.translate(
            (shape.points()[0] / Math.min(1, 2 / this.layerScale)) * shape.scaleX(),
            (shape.points()[1] / Math.min(1, 2 / this.layerScale)) * shape.scaleY()
          );
          con.fillText(Description || '', 0, -shape.attrs.strokeWidth);
          node?.setAttrs({
            Description,
            imageW: Math.abs(this.startPoint.imageX - endPoint.imageX),
            imageH: Math.abs(this.startPoint.imageY - endPoint.imageY),
          });
        } else {
          con.translate(
            (shape.points()[0] / Math.min(1, 2 / this.layerScale)) * shape.scaleX(),
            (shape.points()[1] / Math.min(1, 2 / this.layerScale)) * shape.scaleY()
          );
          con.fillText(shape.attrs.Description || '', 0, -shape.attrs.strokeWidth);
        }
      });
    }
    return node;
  }
}
