import { BaseDrawer, DrawPoint } from './BaseDrawer';
import Konva from 'konva';
import OpenSeadragon from 'openseadragon';
import { Layer } from 'konva/lib/Layer';
import { DrawType, DrawStatus } from '@/views/Marker/components/editorEnum';
import { isMouseLeftOrTouch } from '@/utils/index';

// 魔棒参数选项
export interface WandOptions {
  tolerance: number;
  minRadius?: number;
  maxRadius?: number;
  roughness?: number;
  wandType?: 'brightness' | 'rgb' | 'hsv' | 'lab';
  maxRegionSize?: number;
  searchRadius?: number;
}

// 区域接口
interface Region {
  pixels: Set<number>;
  bounds: { minX: number; minY: number; maxX: number; maxY: number };
  contour: number[];
}

export class WandDrawer extends BaseDrawer<Konva.Group> {
  private _wandConfig: any;
  protected _ratio: any = {
    unit: 'px',
    scales: 1,
  };

  // 图像数据
  private imageData: ImageData | null = null;
  private selectionMask: Uint8Array | null = null;
  private canvasWidth = 0;
  private canvasHeight = 0;

  // 魔棒参数
  private tolerance = 20;
  private wandType: 'brightness' | 'rgb' | 'hsv' | 'lab' = 'brightness';
  private maxRegionSize = 5000; // 最大区域像素数，限制区域大小
  private searchRadius = 100; // 搜索半径（像素），限制搜索范围

  // 状态管理
  private hasSelection = false;
  private isDragging = false;
  private lastProcessedPoint: { x: number; y: number } | null = null;
  private minMoveDistance = 8; // 减少最小移动距离（像素）

  // 数据存储
  private detectedRegions: Region[] = [];
  private regionShapes: Konva.Shape[] = [];
  private visitedPixels: Set<number> = new Set();
  private visitedRegions: Set<string> = new Set(); // 存储已访问的区域标识
  private processedAreas: Set<string> = new Set(); // 存储已处理的区域中心点
  private dragPath: DrawPoint[] = [];

  constructor(layer: Layer, viewer: OpenSeadragon.Viewer, wandConfig: any) {
    super(layer, viewer, wandConfig);
    this._wandConfig = wandConfig;
    this.startPoint = {};
  }

  override start() {
    super.start();
    this.viewer.setMouseNavEnabled(false);
    this.layer.getStage().container().style.cursor = 'crosshair';
    const canvasDiv = this.layer.getStage().container();
    if (canvasDiv) {
      canvasDiv.style.zIndex = '2';
    }
  }

  override end() {
    this.isDragging = false;
    this.viewer.setMouseNavEnabled(true);
    this.layer.getStage().container().style.cursor = 'default';
    const canvasDiv = this.layer.getStage().container();
    if (canvasDiv) {
      canvasDiv.style.zIndex = '1';
    }

    // 创建标记并调用回调
    this.createMarkerAndCallback();

    super.end();
  }

  override drawDown() {
    if (!this.node) {
      this.start();

      // 使用标准的坐标获取方式（与其他工具保持一致）
      this.startPoint = this.curPoint;
      this.isDragging = true;
      this.dragPath = [this.startPoint];
      this.lastProcessedPoint = { x: this.startPoint.x, y: this.startPoint.y };

      // 创建节点
      this.node = this.createNode();
      this.layer.add(this.node);
      this.setNodeId();

      // 异步获取图像数据并执行初始选择
      this.initializeWandSelection();
    }
  }

  /**
   * 初始化魔杖选择
   */
  private async initializeWandSelection() {
    try {
      // 获取图像数据
      await this.getImageDataFromViewer();

      // 检查修饰键
      const addToSelection = this._wandConfig.ev?.shiftKey;
      const subtractFromSelection = this._wandConfig.ev?.altKey;

      // 清除之前的选择（如果不是添加模式）
      if (!addToSelection && !subtractFromSelection) {
        this.clearSelection();
        this.visitedPixels.clear();
        this.visitedRegions.clear();
        this.processedAreas.clear();
      }

      // 执行初始选择
      const hasInitialRegion = await this.performWandSelection(
        this.startPoint.imageX!,
        this.startPoint.imageY!,
        addToSelection,
        subtractFromSelection
      );

      // 如果初始选择成功，标记起始区域为已处理
      if (hasInitialRegion) {
        const gridSize = 12;
        const gridX = Math.floor(this.startPoint.imageX! / gridSize);
        const gridY = Math.floor(this.startPoint.imageY! / gridSize);
        const areaKey = `${gridX}_${gridY}`;
        this.processedAreas.add(areaKey);
      }
    } catch (error) {
      console.error('魔杖初始化失败:', error);
    }
  }

  override drawMove() {
    if (!this.isDragging || !this.imageData) return;

    // 使用标准的坐标获取方式（与其他工具保持一致）
    const currentPoint = this.curPoint;

    // 检查移动距离
    if (this.lastProcessedPoint) {
      const distance = Math.sqrt(
        Math.pow(currentPoint.x - this.lastProcessedPoint.x, 2) +
          Math.pow(currentPoint.y - this.lastProcessedPoint.y, 2)
      );

      if (distance < this.minMoveDistance) {
        return; // 移动距离太小，跳过处理
      }
    }

    // 检查是否已处理过这个区域
    const gridSize = 12;
    const gridX = Math.floor(currentPoint.imageX! / gridSize);
    const gridY = Math.floor(currentPoint.imageY! / gridSize);
    const areaKey = `${gridX}_${gridY}`;

    if (this.processedAreas.has(areaKey)) {
      return; // 已处理过这个区域，跳过
    }

    this.dragPath.push(currentPoint);

    // 异步执行拖动选择
    this.handleDragSelection(currentPoint, areaKey);

    // 更新最后处理的点
    this.lastProcessedPoint = { x: currentPoint.x, y: currentPoint.y };
  }

  /**
   * 处理拖动选择
   */
  private async handleDragSelection(currentPoint: any, areaKey: string) {
    try {
      // 检查修饰键
      const addToSelection = this._wandConfig.ev?.shiftKey;
      const subtractFromSelection = this._wandConfig.ev?.altKey;

      // 执行拖动选择
      const hasNewRegion = await this.performWandSelection(
        currentPoint.imageX!,
        currentPoint.imageY!,
        addToSelection || this.hasSelection,
        subtractFromSelection
      );

      // 只有成功检测到新区域时才标记这个区域为已处理
      if (hasNewRegion) {
        this.processedAreas.add(areaKey);
      }
    } catch (error) {
      console.error('拖动选择失败:', error);
    }
  }

  override drawUp() {
    if (isMouseLeftOrTouch(this._wandConfig.ev)) {
      this.isDragging = false;
      this.status = DrawStatus.drawingCompleted;
      this.end();
    }
  }

  // 创建节点（魔棒工具创建的是一个组，包含多个区域形状）
  createNode() {
    const group = new Konva.Group({
      name: DrawType.wand,
      strokeScaleEnabled: false,
      zoom: this.viewer.viewport.getZoom(),
    });
    return group;
  }

  /**
   * 设置魔棒参数
   */
  setWandOptions(options: WandOptions) {
    if (options.tolerance !== undefined) this.tolerance = options.tolerance;
    if (options.wandType !== undefined) this.wandType = options.wandType;
    if (options.maxRegionSize !== undefined) this.maxRegionSize = options.maxRegionSize;
    if (options.searchRadius !== undefined) this.searchRadius = options.searchRadius;
  }

  /**
   * 从viewer获取图像数据
   */
  private async getImageDataFromViewer(): Promise<ImageData> {
    const canvas = this.viewer.drawer.canvas as HTMLCanvasElement;
    const ctx = canvas.getContext('2d');
    if (!ctx) throw new Error('Cannot get 2D context');

    this.canvasWidth = canvas.width;
    this.canvasHeight = canvas.height;
    this.imageData = ctx.getImageData(0, 0, this.canvasWidth, this.canvasHeight);

    // 初始化选择掩码
    if (!this.selectionMask) {
      this.selectionMask = new Uint8Array(this.canvasWidth * this.canvasHeight);
    }

    return this.imageData;
  }

  /**
   * 检测独立区域（直接从WandEditor.ts复制）
   */
  private async detectRegion(startX: number, startY: number): Promise<Region | null> {
    if (!this.imageData) return null;

    const width = this.canvasWidth;
    const height = this.canvasHeight;
    const data = this.imageData.data;

    // 确保坐标在范围内
    startX = Math.max(0, Math.min(startX, width - 1));
    startY = Math.max(0, Math.min(startY, height - 1));

    // 计算搜索边界（限制在鼠标附近的区域）
    const searchMinX = Math.max(0, startX - this.searchRadius);
    const searchMaxX = Math.min(width - 1, startX + this.searchRadius);
    const searchMinY = Math.max(0, startY - this.searchRadius);
    const searchMaxY = Math.min(height - 1, startY + this.searchRadius);

    // 获取起始点的颜色值
    const startIndex = (startY * width + startX) * 4;
    const startColor = this.getPixelValue(data, startIndex);

    // 使用洪水填充算法检测连通区域（带约束）
    const visited = new Uint8Array(width * height);
    const queue = [{ x: startX, y: startY }];
    const regionPixels = new Set<number>();

    let minX = startX,
      maxX = startX;
    let minY = startY,
      maxY = startY;

    while (queue.length > 0 && regionPixels.size < this.maxRegionSize) {
      const { x, y } = queue.shift()!;
      const index = y * width + x;

      if (visited[index]) continue;
      if (x < 0 || x >= width || y < 0 || y >= height) continue;

      // 限制搜索范围：只在指定半径内搜索
      if (x < searchMinX || x > searchMaxX || y < searchMinY || y > searchMaxY) {
        continue;
      }

      visited[index] = 1;

      // 获取当前像素的颜色值
      const currentIndex = (y * width + x) * 4;
      const currentColor = this.getPixelValue(data, currentIndex);

      // 检查颜色相似性
      if (this.isColorSimilar(startColor, currentColor)) {
        regionPixels.add(index);

        // 更新边界
        minX = Math.min(minX, x);
        maxX = Math.max(maxX, x);
        minY = Math.min(minY, y);
        maxY = Math.max(maxY, y);

        // 添加8邻域像素到队列（只有在区域大小未超限时）
        if (regionPixels.size < this.maxRegionSize) {
          const neighbors = [
            { x: x + 1, y: y },
            { x: x - 1, y: y },
            { x: x, y: y + 1 },
            { x: x, y: y - 1 },
            { x: x + 1, y: y + 1 },
            { x: x - 1, y: y + 1 },
            { x: x + 1, y: y - 1 },
            { x: x - 1, y: y - 1 },
          ];

          neighbors.forEach((neighbor) => {
            const neighborIndex = neighbor.y * width + neighbor.x;
            if (
              neighbor.x >= 0 &&
              neighbor.x < width &&
              neighbor.y >= 0 &&
              neighbor.y < height &&
              !visited[neighborIndex] &&
              // 确保邻居也在搜索范围内
              neighbor.x >= searchMinX &&
              neighbor.x <= searchMaxX &&
              neighbor.y >= searchMinY &&
              neighbor.y <= searchMaxY
            ) {
              queue.push(neighbor);
            }
          });
        }
      }
    }

    if (regionPixels.size === 0) return null;

    // 提取轮廓
    const contour = this.extractContour(regionPixels, {
      minX,
      minY,
      maxX,
      maxY,
    });

    return {
      pixels: regionPixels,
      bounds: { minX, minY, maxX, maxY },
      contour: contour,
    };
  }

  /**
   * 获取像素值（根据wandType）- 从WandEditor.ts复制
   */
  private getPixelValue(data: Uint8ClampedArray, index: number): any {
    const r = data[index];
    const g = data[index + 1];
    const b = data[index + 2];

    switch (this.wandType) {
      case 'brightness':
        return 0.299 * r + 0.587 * g + 0.114 * b;
      case 'rgb':
        return { r, g, b };
      case 'hsv':
        return this.rgbToHsv(r, g, b);
      case 'lab':
        return this.rgbToLab(r, g, b);
      default:
        return 0.299 * r + 0.587 * g + 0.114 * b;
    }
  }

  /**
   * 检查颜色相似性 - 从WandEditor.ts复制
   */
  private isColorSimilar(color1: any, color2: any): boolean {
    const distance = this.colorDistance(color1, color2);
    return distance <= this.tolerance;
  }

  /**
   * 计算颜色距离 - 从WandEditor.ts复制
   */
  private colorDistance(color1: any, color2: any): number {
    switch (this.wandType) {
      case 'brightness':
        return Math.abs(color1 - color2);
      case 'rgb':
        return Math.sqrt(
          Math.pow(color1.r - color2.r, 2) +
            Math.pow(color1.g - color2.g, 2) +
            Math.pow(color1.b - color2.b, 2)
        );
      case 'hsv':
        return Math.sqrt(
          Math.pow(color1.h - color2.h, 2) +
            Math.pow(color1.s - color2.s, 2) +
            Math.pow(color1.v - color2.v, 2)
        );
      case 'lab':
        return Math.sqrt(
          Math.pow(color1.l - color2.l, 2) +
            Math.pow(color1.a - color2.a, 2) +
            Math.pow(color1.b - color2.b, 2)
        );
      default:
        return Math.abs(color1 - color2);
    }
  }

  /**
   * RGB转HSV - 从WandEditor.ts复制
   */
  private rgbToHsv(r: number, g: number, b: number): { h: number; s: number; v: number } {
    r /= 255;
    g /= 255;
    b /= 255;

    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    const diff = max - min;

    let h = 0;
    if (diff !== 0) {
      if (max === r) h = ((g - b) / diff) % 6;
      else if (max === g) h = (b - r) / diff + 2;
      else h = (r - g) / diff + 4;
    }
    h = Math.round(h * 60);
    if (h < 0) h += 360;

    const s = max === 0 ? 0 : diff / max;
    const v = max;

    return { h, s: s * 100, v: v * 100 };
  }

  /**
   * RGB转LAB - 从WandEditor.ts复制
   */
  private rgbToLab(r: number, g: number, b: number): { l: number; a: number; b: number } {
    // RGB to XYZ
    r = r / 255;
    g = g / 255;
    b = b / 255;

    r = r > 0.04045 ? Math.pow((r + 0.055) / 1.055, 2.4) : r / 12.92;
    g = g > 0.04045 ? Math.pow((g + 0.055) / 1.055, 2.4) : g / 12.92;
    b = b > 0.04045 ? Math.pow((b + 0.055) / 1.055, 2.4) : b / 12.92;

    let x = (r * 0.4124 + g * 0.3576 + b * 0.1805) / 0.95047;
    let y = (r * 0.2126 + g * 0.7152 + b * 0.0722) / 1.0;
    let z = (r * 0.0193 + g * 0.1192 + b * 0.9505) / 1.08883;

    x = x > 0.008856 ? Math.pow(x, 1 / 3) : 7.787 * x + 16 / 116;
    y = y > 0.008856 ? Math.pow(y, 1 / 3) : 7.787 * y + 16 / 116;
    z = z > 0.008856 ? Math.pow(z, 1 / 3) : 7.787 * z + 16 / 116;

    const l = 116 * y - 16;
    const a = 500 * (x - y);
    const bLab = 200 * (y - z);

    return { l, a: a, b: bLab };
  }

  /**
   * 提取区域轮廓 - 从WandEditor.ts完整复制
   */
  private extractContour(
    pixels: Set<number>,
    bounds: { minX: number; minY: number; maxX: number; maxY: number }
  ): number[] {
    const width = this.canvasWidth;
    const contourPoints: Array<[number, number]> = [];

    // 创建局部二值图像
    const localWidth = bounds.maxX - bounds.minX + 3;
    const localHeight = bounds.maxY - bounds.minY + 3;
    const binaryImage = new Uint8Array(localWidth * localHeight);

    // 填充二值图像
    pixels.forEach((idx) => {
      const x = idx % width;
      const y = Math.floor(idx / width);
      const localX = x - bounds.minX + 1;
      const localY = y - bounds.minY + 1;
      const localIdx = localY * localWidth + localX;
      binaryImage[localIdx] = 1;
    });

    // 使用Moore邻域追踪算法提取轮廓
    const startX = 1;
    const startY = 1;

    // 找到第一个边界点
    let found = false;
    let currentX = startX;
    let currentY = startY;

    for (let y = 0; y < localHeight && !found; y++) {
      for (let x = 0; x < localWidth && !found; x++) {
        const idx = y * localWidth + x;
        if (binaryImage[idx] === 1) {
          // 检查是否是边界点
          const hasEmptyNeighbor =
            (x > 0 && binaryImage[idx - 1] === 0) ||
            (x < localWidth - 1 && binaryImage[idx + 1] === 0) ||
            (y > 0 && binaryImage[idx - localWidth] === 0) ||
            (y < localHeight - 1 && binaryImage[idx + localWidth] === 0);

          if (hasEmptyNeighbor) {
            currentX = x;
            currentY = y;
            found = true;
          }
        }
      }
    }

    if (!found) return [];

    // Moore邻域追踪
    const startIdx = currentY * localWidth + currentX;
    const directions = [
      [-1, -1],
      [0, -1],
      [1, -1],
      [1, 0],
      [1, 1],
      [0, 1],
      [-1, 1],
      [-1, 0],
    ];

    let direction = 0;
    const maxSteps = pixels.size * 2;
    let steps = 0;

    do {
      // 添加当前点到轮廓
      const worldX = currentX + bounds.minX - 1;
      const worldY = currentY + bounds.minY - 1;
      contourPoints.push([worldX, worldY]);

      // 寻找下一个轮廓点
      let foundNext = false;
      for (let i = 0; i < 8; i++) {
        const nextDir = (direction + 6 + i) % 8;
        const [dx, dy] = directions[nextDir];
        const nextX = currentX + dx;
        const nextY = currentY + dy;

        if (nextX >= 0 && nextX < localWidth && nextY >= 0 && nextY < localHeight) {
          const nextIdx = nextY * localWidth + nextX;
          if (binaryImage[nextIdx] === 1) {
            currentX = nextX;
            currentY = nextY;
            direction = nextDir;
            foundNext = true;
            break;
          }
        }
      }

      if (!foundNext) break;
      steps++;
    } while (currentY * localWidth + currentX !== startIdx && steps < maxSteps);

    // 简化轮廓
    const simplified = this.simplifyContour(contourPoints, 1.5);

    // 转换为Konva坐标
    const konvaPoints: number[] = [];
    simplified.forEach(([x, y]: [number, number]) => {
      const konvaPoint = this._konvaPointFromPixl(x, y);
      konvaPoints.push(konvaPoint.x, konvaPoint.y);
    });

    return konvaPoints;
  }

  /**
   * 简化轮廓（Douglas-Peucker算法）- 从WandEditor.ts复制
   */
  private simplifyContour(
    points: Array<[number, number]>,
    epsilon: number
  ): Array<[number, number]> {
    if (points.length < 3) return points;

    // 找到距离最远的点
    let maxDist = 0;
    let maxIdx = 0;
    const start = points[0];
    const end = points[points.length - 1];

    for (let i = 1; i < points.length - 1; i++) {
      const dist = this.pointLineDistance(points[i], start, end);
      if (dist > maxDist) {
        maxDist = dist;
        maxIdx = i;
      }
    }

    // 如果最大距离大于阈值，递归简化
    if (maxDist > epsilon) {
      const left = this.simplifyContour(points.slice(0, maxIdx + 1), epsilon);
      const right = this.simplifyContour(points.slice(maxIdx), epsilon);
      return [...left.slice(0, -1), ...right];
    } else {
      return [start, end];
    }
  }

  /**
   * 计算点到线的距离 - 从WandEditor.ts复制
   */
  private pointLineDistance(
    point: [number, number],
    lineStart: [number, number],
    lineEnd: [number, number]
  ): number {
    const [x, y] = point;
    const [x1, y1] = lineStart;
    const [x2, y2] = lineEnd;

    const A = y2 - y1;
    const B = x1 - x2;
    const C = x2 * y1 - x1 * y2;

    return Math.abs(A * x + B * y + C) / Math.sqrt(A * A + B * B);
  }

  /**
   * 清除选择
   */
  private clearSelection() {
    // 清除所有形状
    this.regionShapes.forEach((shape) => shape.destroy());

    this.regionShapes = [];
    this.detectedRegions = [];
    this.visitedPixels.clear();
    this.visitedRegions.clear();
    this.processedAreas.clear();
    this.lastProcessedPoint = null;

    if (this.selectionMask) {
      this.selectionMask.fill(0);
      this.hasSelection = false;
    }

    this.layer.draw();
  }

  /**
   * 执行魔棒选择（支持拖动模式）
   * 直接使用WandEditor.ts的算法
   */
  private async performWandSelection(
    imageX: number,
    imageY: number,
    _addToSelection = false,
    subtractFromSelection = false
  ): Promise<boolean> {
    try {
      if (!this.imageData) {
        console.error('❌ 没有图像数据');
        return false;
      }
      // 将图像坐标转换为画布坐标（与原始WandEditor保持一致）
      const pt = new OpenSeadragon.Point(Number(imageX), Number(imageY));
      const viewportPoint = this.viewer.viewport.imageToViewportCoordinates(pt.x, pt.y);
      const pixelPoint = this.viewer.viewport.pixelFromPoint(viewportPoint);

      const x = Math.round(pixelPoint.x);
      const y = Math.round(pixelPoint.y);

      // 检测区域
      const region = await this.detectRegion(x, y);

      if (region && region.pixels.size > 10) {
        // 检查是否有足够的新像素（避免重复处理相同区域）
        const newPixels = new Set<number>();
        region.pixels.forEach((pixel: number) => {
          if (!this.visitedPixels.has(pixel)) {
            newPixels.add(pixel);
          }
        });

        // 只有当新像素数量足够时才处理
        if (newPixels.size > 5) {
          // 将新像素标记为已访问
          newPixels.forEach((pixel: number) => this.visitedPixels.add(pixel));

          if (subtractFromSelection) {
            // 从选择中减去
            this.subtractRegionFromSelection(region);
          } else {
            // 添加到选择（会自动合并重叠区域）
            this.addRegionToSelection(region);
          }
          return true; // 成功处理了新区域
        } else {
          console.error('⚠️ 新像素数量不足，跳过处理');
        }
      } else {
        console.error('⚠️ 区域无效或像素数量不足');
      }
      return false; // 没有处理新区域
    } catch (error) {
      console.error('魔棒选择错误:', error);
      return false;
    }
  }

  /**
   * 添加区域到选择
   */
  private addRegionToSelection(region: Region) {
    // 检查是否与现有区域重叠
    const overlappingRegions: number[] = [];
    for (let i = 0; i < this.detectedRegions.length; i++) {
      if (this.regionsOverlap(this.detectedRegions[i], region)) {
        overlappingRegions.push(i);
      }
    }

    if (overlappingRegions.length > 0) {
      // 合并重叠的区域
      const mergedRegion = this.mergeRegions([
        region,
        ...overlappingRegions.map((i) => this.detectedRegions[i]),
      ]);

      // 移除旧的重叠区域（从后往前删除，避免索引变化）
      overlappingRegions.sort((a, b) => b - a);
      overlappingRegions.forEach((index) => {
        this.regionShapes[index]?.destroy();
        this.detectedRegions.splice(index, 1);
        this.regionShapes.splice(index, 1);
      });

      // 添加合并后的区域
      this.detectedRegions.push(mergedRegion);
      this.createRegionShape(mergedRegion);
    } else {
      // 没有重叠，直接添加
      this.detectedRegions.push(region);
      this.createRegionShape(region);
    }

    this.hasSelection = true;
  }

  /**
   * 从选择中减去区域
   */
  private subtractRegionFromSelection(region: Region) {
    // 简化实现：移除重叠的区域
    const newRegions = this.detectedRegions.filter((existingRegion) => {
      return !this.regionsOverlap(existingRegion, region);
    });

    if (newRegions.length !== this.detectedRegions.length) {
      this.detectedRegions = newRegions;
      this.redrawAllRegions();
    }
  }

  /**
   * 检查两个区域是否重叠
   */
  private regionsOverlap(region1: Region, region2: Region): boolean {
    // 检查边界框是否重叠
    if (
      region1.bounds.maxX < region2.bounds.minX ||
      region2.bounds.maxX < region1.bounds.minX ||
      region1.bounds.maxY < region2.bounds.minY ||
      region2.bounds.maxY < region1.bounds.minY
    ) {
      return false;
    }

    // 检查像素级重叠
    for (const pixel of region1.pixels) {
      if (region2.pixels.has(pixel)) {
        return true;
      }
    }
    return false;
  }

  /**
   * 合并多个区域
   */
  private mergeRegions(regions: Region[]): Region {
    const mergedPixels = new Set<number>();
    let minX = Infinity,
      maxX = -Infinity;
    let minY = Infinity,
      maxY = -Infinity;

    for (const region of regions) {
      region.pixels.forEach((pixel) => mergedPixels.add(pixel));
      minX = Math.min(minX, region.bounds.minX);
      maxX = Math.max(maxX, region.bounds.maxX);
      minY = Math.min(minY, region.bounds.minY);
      maxY = Math.max(maxY, region.bounds.maxY);
    }

    const contour = this.extractContour(mergedPixels, { minX, minY, maxX, maxY });

    return {
      pixels: mergedPixels,
      bounds: { minX, minY, maxX, maxY },
      contour,
    };
  }

  /**
   * 重绘所有区域
   */
  private redrawAllRegions() {
    // 清除现有形状
    this.regionShapes.forEach((shape) => shape.destroy());
    this.regionShapes = [];

    // 重新创建所有区域形状
    this.detectedRegions.forEach((region) => {
      this.createRegionShape(region);
    });

    this.layer.draw();
  }

  /**
   * 创建区域形状
   */
  private createRegionShape(region: Region) {
    if (region.contour.length < 6) return; // 至少需要3个点

    // 创建封闭多边形
    const shape = new Konva.Line({
      points: region.contour,
      stroke: '#edea16',
      strokeWidth: 2,
      strokeScaleEnabled: false,
      closed: true,
      tension: 0.1,
      lineJoin: 'round',
      lineCap: 'round',
    });

    // 添加点击事件
    shape.on('click', () => {
      // console.log('点击了区域，像素数:', region.pixels.size);
    });

    this.regionShapes.push(shape);

    this.layer.add(shape);
    this.layer.draw();
  }

  /**
   * 创建标记并调用回调
   */
  private createMarkerAndCallback() {
    if (this.regionShapes.length > 0) {
      // 创建包含所有区域的组节点
      if (!this.node) {
        this.node = this.createNode();
        this.layer.add(this.node);
        this.setNodeId();
      }

      // 将所有区域形状添加到组中
      this.regionShapes.forEach((shape) => {
        this.node!.add(shape);
      });

      // 设置节点属性
      this.node.setAttrs({
        pixelCoords: {
          regionCount: this.regionShapes.length,
          regions: this.detectedRegions.map((region) => ({
            pixelCount: region.pixels.size,
            bounds: region.bounds,
          })),
        },
        Description: `魔棒选择 - ${this.regionShapes.length} 个区域`,
      });

      this.layer.draw();
    }
  }
}
