// /**
//  * 绘画器
//  */
// // import _ from 'lodash';

// import { Circle, CircleConfig } from 'konva/lib/shapes/Circle';
// import { Shape, ShapeConfig } from 'konva/lib/Shape';
// import { Ellipse, EllipseConfig } from 'konva/lib/shapes/Ellipse';
// import { Line, LineConfig } from 'konva/lib/shapes/Line';
// import { Rect, RectConfig } from 'konva/lib/shapes/Rect';
// import { Image as KonvaImage, ImageConfig } from 'konva/lib/shapes/Image';
// import Konva from 'konva';
// import { Layer } from 'konva/lib/Layer';
// import { DrawStatus, DrawType, HistoryRecordTypes } from '@/views/Marker/components/editorEnum';
// import { Arrow, ArrowConfig } from 'konva/lib/shapes/Arrow';
// import { Transformer } from 'konva/lib/shapes/Transformer';
// import Label from '@/views/Marker/components//label';
// import { Editor } from '@/views/Marker/components//editor';
// import OpenSeadragon, { Point } from 'openseadragon';
// import { Group } from 'konva/lib/Group';
// import mitt, { type Emitter } from 'mitt';
// import { changeArrowPointerByZoom, isMouseLeftOrTouch } from '@/utils/index';
// export interface DrawerConfig {
//   layer: Layer; //画图层
//   viewer: OpenSeadragon.Viewer; //画图层
// }

// export interface DrawPoint {
//   x: number;
//   y: number;
//   imageX?: number;
//   imageY?: number;
//   pcx?: number;
//   pcy?: number;
// }

// export class Drawer {
//   _drawType!: DrawType | null; //绘画形状类型

//   _layer: Layer; //画图层

//   _node!: Rect | Circle | Ellipse | Line | Arrow | null | Group; //绘画元素

//   _status: DrawStatus = DrawStatus.end; //画图状态

//   _transformer!: Transformer; //用来调整元素的调整器

//   _label: Label;

//   _viewer: OpenSeadragon.Viewer;

//   _minWidth = 0.06;
//   _minHeight = 0.06;

//   _drawEndCallback?: Function;
//   _selectCallback?: Function;
//   _cancelSelect?: Function;

//   history: any[] = [{}]; // 用来存放node的历史
//   historyIndex = -1;

//   protected emitter: Emitter<any> = mitt();
//   on: Emitter<any>['on'];
//   off: Emitter<any>['off'];
//   emit: Emitter<any>['emit'];
//   constructor(config: DrawerConfig) {
//     this._layer = config.layer;
//     this._viewer = config.viewer;
//     this._label = Label.getInstance();
//     this.on = this.emitter.on.bind(this.emitter);
//     this.off = this.emitter.off.bind(this.emitter);
//     this.emit = this.emitter.emit.bind(this.emitter);
//     this.updateHistory();
//   }

//   //是否有绘画对象
//   get hasDrawer(): boolean {
//     return this._node ? true : false;
//   }

//   //绘画状态
//   get status(): DrawStatus {
//     return this._status;
//   }

//   //缩放大小
//   get scale(): number {
//     return this._node ? this._node.scaleX() : 1;
//   }
//   //缩放大小
//   get layerScale(): number {
//     return this._layer ? this._layer.scaleX() : 1;
//   }

//   //绘画元素
//   get node(): Rect | Circle | Ellipse | Line | null | Arrow | Group {
//     return this._node;
//   }

//   setStatus(status: DrawStatus) {
//     this._status = status;
//     return this._status;
//   }

//   setDrawEndCallback(callback?: Function) {
//     if (callback) {
//       this._drawEndCallback = callback;
//     }
//   }

//   prevHistory() {
//     console.log(this.history, 'history', this.historyIndex);
//     let record = null;
//     // if (this.historyIndex === 0) {
//     //   record = this.history[0];
//     // } else {
//     record = this.history[this.historyIndex - 1];
//     // }

//     console.log(record, 'record');
//     if (record) {
//       this.addMarker(record);
//       this.historyIndex--;
//       // 历史变化事件
//       this.emit('history-change', {
//         records: this.history,
//         index: this.historyIndex,
//       });
//     }
//   }

//   nextHistory() {
//     const record = this.history[this.historyIndex + 1];
//     if (record) {
//       this.addMarker(record);
//       this.historyIndex++;
//       // 历史变化事件
//       this.emit('history-change', {
//         records: this.history,
//         index: this.historyIndex,
//       });
//     }
//   }

//   updateHistory(type?: HistoryRecordTypes) {
//     this.history.splice(this.historyIndex + 1);
//     this.history.push(JSON.parse(JSON.stringify(this._label.labels)));
//     this.historyIndex = this.history.length - 1;
//     this.emit('history-change', {
//       records: this.history,
//       index: this.historyIndex,
//     });
//   }

//   //添加标注
//   addMarker(markers: any): void {
//     console.log(markers, 'markers');
//     this._layer.removeChildren();
//     if (markers.length > 0) {
//       for (let marker of markers) {
//         const typeName = marker.name as keyof typeof shapeType;
//         if (!typeName) {
//           return console.error('name为空');
//         }
//         const shapeType = {
//           line: Line,
//           arrow: Arrow,
//           rect: Rect,
//           circle: Circle,
//           ellipse: Ellipse,
//           angle: Group,
//           ring: Group,
//           flag: Image,
//           foldLine: Line,
//           polygon: Line,
//           closedCurve: Line,
//           freeCurve: Line,
//         };
//         let node: any;
//         if (shapeType[typeName]) {
//           node = new shapeType[typeName]();
//         }
//         // 同心圆和夹角是通过Group实现的 需要额外做处理
//         if (shapeType[typeName] !== Group) {
//           node?.setAttrs({ ...marker.attrs, draggable: false });
//           node?.id(marker.id);
//         } else {
//           marker.children.forEach((item: any) => {
//             const type = item.className;
//             if (type) {
//               const shape = new (Konva as any)[type]();
//               shape?.setAttrs({ ...item.attrs, draggable: false });
//               shape?.id(item.id);
//               node?.add(shape);
//             }
//           });
//         }
//         this._layer.add(node as Shape<ShapeConfig>);
//       }
//     }
//   }

//   // 获取当前鼠标位置
//   getCurPoint() {
//     // const touchPos = this._layer.getStage().getPointerPosition();
//     // const x = touchPos?.x as number;
//     // const y = touchPos?.y as number;
//     // const curPoint = this.konvaPointFromPixl(x, y);
//     // return curPoint;

//     const touchPos = this._layer.getStage().getPointerPosition();
//     const x = touchPos?.x as number;
//     const y = touchPos?.y as number;
//     const isFlipped = document
//       .getElementById('konva-overlay')
//       ?.style.transform.includes('scaleX(-1)');
//     let adjustedX = x;
//     if (isFlipped) {
//       const stageWidth = this._layer.getStage().width();
//       adjustedX = stageWidth - x; // 进行水平翻转
//     }
//     const curPoint = this.konvaPointFromPixl(adjustedX, y);
//     return curPoint;
//   }

//   setNodeId() {
//     const id = new Date().getTime();
//     this._node?.id(`${id}`);
//   }

//   // 画矩形
//   _startPoint: any = {};
//   drawRect(rectConfig: RectConfig): Rect {
//     this.start();
//     const { ev } = rectConfig;
//     const curPoint = this.getCurPoint();
//     const { x, y, width, height } = this.getRect(this._startPoint, curPoint);
//     const handlers: { [key: string]: () => void } = {
//       contextmenu: () => {
//         // ev.evt.preventDefault();
//         // this.end();
//       },
//       mousedown: () => {
//         // if (this._node) return;
//         this._startPoint = curPoint;
//         this._node = new Konva.Rect({
//           ...rectConfig.config,
//           x,
//           y,
//           width,
//           height,
//           name: DrawType.rect,
//           strokeScaleEnabled: false,
//           zoom: this._viewer.viewport.getZoom(),
//         });
//         this._layer.add(this._node);
//         this.setNodeId();
//       },
//       mousemove: () => {
//         this._node?.setAttrs({
//           x,
//           y,
//           width,
//           height,
//         });
//       },
//       mouseup: () => {
//         if (isMouseLeftOrTouch(ev)) {
//           // 鼠标左键
//           if (width < this._minWidth || height < this._minHeight) {
//             this._nodeDestroy();
//           }
//           this.end();
//         }
//       },
//     };
//     const handler = handlers[rectConfig.handler];
//     if (handler) handler();
//     return this._node as Rect;
//   }

//   //画椭圆
//   drawEllipse(ellipseConfig: EllipseConfig): Ellipse {
//     this.start();
//     const { ev } = ellipseConfig;
//     const curPoint = this.getCurPoint();
//     const { x, y, width, height } = this.getRect(this._startPoint, curPoint);
//     const radiusX = width / 2;
//     const radiusY = height / 2;
//     const centerX = x + width / 2;
//     const centerY = y + height / 2;
//     const handlers: { [key: string]: () => void } = {
//       contextmenu: () => {
//         // ev.evt.preventDefault();
//         // this.end();
//       },
//       mousedown: () => {
//         // if (this._node) return;
//         this._startPoint = curPoint;
//         this._node = new Konva.Ellipse({
//           ...ellipseConfig.config,
//           x: centerX,
//           y: centerY,
//           radiusX,
//           radiusY,
//           name: DrawType.ellipse,
//           strokeScaleEnabled: false,
//           zoom: this._viewer.viewport.getZoom(),
//         });
//         this._layer.add(this._node);
//         this.setNodeId();
//       },
//       mousemove: () => {
//         this._node?.setAttrs({
//           x: centerX,
//           y: centerY,
//           radiusX,
//           radiusY,
//         });
//       },
//       mouseup: () => {
//         if (isMouseLeftOrTouch(ev)) {
//           // 鼠标左键
//           if (this._node) {
//             // 鼠标左键
//             if (width < this._minWidth || height < this._minHeight) {
//               this._nodeDestroy();
//             }
//             this.end();
//           }
//         }
//       },
//     };
//     const handler = handlers[ellipseConfig.handler];
//     if (handler) handler();
//     return this._node as Ellipse;
//   }

//   // 画直线
//   drawLine(lineConfig: LineConfig): Line {
//     this.start();
//     const { ev } = lineConfig;
//     const curPoint = this.getCurPoint();
//     const { x, y, width, height } = this.getRect(this._startPoint, curPoint);
//     const handlers: { [key: string]: () => void } = {
//       // contextmenu: () => {
//       //   ev.evt.preventDefault();
//       //   this.end();
//       // },
//       mousedown: () => {
//         // if (this._node) return;
//         this._startPoint = curPoint;
//         this._node = new Konva.Line({
//           ...lineConfig.config,
//           points: [this._startPoint.x, this._startPoint.y],
//           hitStrokeWidth: 30,
//           name: DrawType.line,
//           strokeScaleEnabled: false,
//           zoom: this._viewer.viewport.getZoom(),
//         });
//         this._layer.add(this._node);
//         this.setNodeId();
//       },
//       mousemove: () => {
//         this._node?.setAttrs({
//           points: [this._startPoint.x, this._startPoint.y, curPoint.x, curPoint.y],
//         });
//       },
//       mouseup: () => {
//         if (isMouseLeftOrTouch(ev)) {
//           // 鼠标左键
//           if (width < this._minWidth || height < this._minHeight) {
//             this._nodeDestroy();
//           }
//           // this._status = DrawStatus.drawingCompleted;
//           this.end();
//         }
//       },
//     };
//     const handler = handlers[lineConfig.handler];
//     if (handler) handler();
//     return this._node as Line;
//   }

//   //画箭头
//   drawArrow(arrowConfig: ArrowConfig): Arrow {
//     this.start();
//     const { ev } = arrowConfig;
//     const curPoint = this.getCurPoint();
//     const { x, y, width, height } = this.getRect(this._startPoint, curPoint);
//     const drawArrow = [
//       this._startPoint.x,
//       this._startPoint.y,
//       width,
//       height,
//       curPoint.x,
//       curPoint.y,
//     ];
//     let resultWidth = 0;
//     let resultHeight = 0;
//     if (drawArrow[0] > drawArrow[4] && drawArrow[1] > drawArrow[5]) {
//       resultWidth = -drawArrow[2];
//       resultHeight = -drawArrow[3];
//     } else if (drawArrow[0] > drawArrow[4] && drawArrow[1] < drawArrow[5]) {
//       resultWidth = -drawArrow[2];
//       resultHeight = drawArrow[3];
//     } else if (drawArrow[0] < drawArrow[4] && drawArrow[1] > drawArrow[5]) {
//       resultWidth = drawArrow[2];
//       resultHeight = -drawArrow[3];
//     } else if (drawArrow[0] < drawArrow[4] && drawArrow[1] < drawArrow[5]) {
//       resultWidth = drawArrow[2];
//       resultHeight = drawArrow[3];
//     }
//     const handlers: { [key: string]: () => void } = {
//       // contextmenu: () => {
//       //   ev.evt.preventDefault();
//       //   this.end();
//       // },
//       mousedown: () => {
//         // if (this._node) return;
//         this._startPoint = curPoint;
//         this._node = new Konva.Arrow({
//           ...arrowConfig.config,
//           x: curPoint.x,
//           y: curPoint.y,
//           points: [0, 0, resultWidth, resultHeight],
//           hitStrokeWidth: 30,
//           name: DrawType.arrow,
//           strokeScaleEnabled: false,
//           zoom: this._viewer.viewport.getZoom(),
//         });
//         changeArrowPointerByZoom(this._node as Arrow, this._viewer.viewport.getZoom());
//         this._node.setAttr('fill', this._node.getAttr('stroke'));
//         this._layer.add(this._node);
//         this.setNodeId();
//       },
//       mousemove: () => {
//         this._node?.setAttrs({
//           x: drawArrow[0],
//           y: drawArrow[1],
//           points: [0, 0, resultWidth, resultHeight],
//         });
//       },
//       mouseup: () => {
//         if (isMouseLeftOrTouch(ev)) {
//           // 鼠标左键
//           if (width < this._minWidth || height < this._minHeight) {
//             this._nodeDestroy();
//           }
//           this.end();
//         }
//       },
//     };
//     const handler = handlers[arrowConfig.handler];
//     if (handler) handler();
//     return this._node as Arrow;
//   }

//   //画旗子
//   drawFlag(imageConfig: ImageConfig): Konva.Image {
//     this.start();
//     const { ev } = imageConfig;
//     const curPoint = this.getCurPoint();
//     // const { x, y } = this.getRect(this._startPoint, curPoint);
//     const handlers: { [key: string]: () => void } = {
//       contextmenu: () => {
//         // ev.evt.preventDefault();
//         // this.end();
//       },
//       mousedown: () => {
//         // if (this._node) return;
//         // this._startPoint = curPoint;
//         const imageObj = new Image();
//         const flagSrc =
//           'data:image/png;base64,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';
//         imageObj.src = flagSrc;
//         this._node = new Konva.Image({
//           x: curPoint.x - 10,
//           y: curPoint.y - 50,
//           image: imageObj,
//           width: 50,
//           height: 50,
//           name: DrawType.flag,
//           strokeScaleEnabled: false,
//         });
//         this._layer.add(this._node);
//         this.setNodeId();
//       },
//       mousemove: () => {
//         this._node?.setAttrs({
//           x: curPoint.x - 10,
//           y: curPoint.y - 50,
//         });
//       },
//       mouseup: () => {
//         if (isMouseLeftOrTouch(ev)) {
//           // 鼠标左键
//           if (this._node) {
//             this.end();
//           }
//         }
//       },
//     };
//     const handler = handlers[imageConfig.handler];
//     if (handler) handler();
//     return this._node as Konva.Image;
//   }

//   // 画圆形
//   drawCircle(circleConfig: CircleConfig) {
//     this.start();
//     const { ev } = circleConfig;
//     const curPoint = this.getCurPoint();
//     const { x, y, width, height } = this.getRect(this._startPoint, curPoint);
//     const handlers: { [key: string]: () => void } = {
//       contextmenu: () => {
//         // ev.evt.preventDefault();
//         // this.end();
//       },
//       mousedown: () => {
//         // if (this._node) return;
//         this._startPoint = curPoint;
//         this._node = new Konva.Circle({
//           ...circleConfig.config,
//           x: x,
//           y: y,
//           name: DrawType.circle,
//           strokeScaleEnabled: false,
//           zoom: this._viewer.viewport.getZoom(),
//         });
//         this._layer.add(this._node);
//         this.setNodeId();
//       },
//       mousemove: () => {
//         const dx = Math.abs(curPoint.x - this._startPoint.x);
//         const dy = Math.abs(curPoint.y - this._startPoint.y);
//         const radius = (dx ** 2 + dy ** 2) ** (1 / 2);
//         this._node?.setAttrs({
//           radius,
//         });
//       },
//       mouseup: () => {
//         if (isMouseLeftOrTouch(ev)) {
//           // 鼠标左键
//           if (width < this._minWidth || height < this._minHeight) {
//             this._nodeDestroy();
//           }
//           this.end();
//         }
//       },
//     };
//     const handler = handlers[circleConfig.handler];
//     if (handler) handler();
//     return this._node as Line;
//   }

//   _anglePoints: any[] = []; // 画夹角需要的点数组

//   // 获取度数
//   getAngle(p1: { x: number; y: number }, p2: { x: number; y: number }) {
//     // 还需要抛出一个逆时针角度 来方便进行计算角度文本的坐标
//     const { x: x1, y: y1 } = p1;
//     const { x: x2, y: y2 } = p2;
//     const dot = x1 * x2 + y1 * y2;
//     const det = x1 * y2 - y1 * x2;
//     const angle = (Math.atan2(det, dot) / Math.PI) * 180;
//     console.log(angle, 'angle');
//     return {
//       CounterclockwiseRotationAngle: angle,
//       angle: (angle + 360) % 360,
//     };
//   }

//   // 计算角度文本的坐标
//   rotatePoint(
//     x1: number,
//     y1: number,
//     x2: number,
//     y2: number,
//     angleDeg: number
//   ): { x: number; y: number } {
//     console.log(angleDeg, 'angleDegangleDeg');
//     const angleRad = angleDeg;
//     const dx = x2 - x1;
//     const dy = y2 - y1;

//     const x2New = x1 + dx * Math.cos(angleRad) - dy * Math.sin(angleRad);
//     const y2New = y1 + dx * Math.sin(angleRad) + dy * Math.cos(angleRad);
//     return {
//       x: x2New,
//       y: y2New,
//     };
//   }

//   drawAngle(angleConfig: any) {
//     this.start();
//     const { ev } = angleConfig;
//     const curPoint = this.getCurPoint();
//     const handlers: { [key: string]: () => void } = {
//       contextmenu: () => {
//         // ev.evt.preventDefault();
//       },
//       mousedown: () => {},
//       mousemove: () => {
//         if (this._node) {
//           const angleLine = (this._node as Group).getChildren?.((node) => {
//             return node.attrs.name === 'angleLine';
//           })[0] as Line;
//           (angleLine as Line)?.setAttrs?.({
//             points: [...this._anglePoints, curPoint.x, curPoint.y],
//           });
//         }
//       },
//       mouseup: () => {
//         const button = ev.evt.button;
//         if (isMouseLeftOrTouch(ev)) {
//           // 鼠标左键
//           angleConfig.ev.evt.stopPropagation();
//           angleConfig.ev.evt.cancelBubble = true;
//           this._anglePoints = [...this._anglePoints, curPoint.x, curPoint.y];
//           if (this._anglePoints.length >= 6 && this._node) {
//             const { angle: degree, CounterclockwiseRotationAngle } = this.getAngle(
//               {
//                 x: this._anglePoints[0] - this._anglePoints[2],
//                 y: this._anglePoints[1] - this._anglePoints[3],
//               },
//               {
//                 x: this._anglePoints[4] - this._anglePoints[2],
//                 y: this._anglePoints[5] - this._anglePoints[3],
//               }
//             );
//             const BPoint = {
//               x: (this._anglePoints[0] + this._anglePoints[2]) / 2,
//               y: (this._anglePoints[1] + this._anglePoints[3]) / 2,
//             };
//             const APoint = {
//               x: this._anglePoints[2],
//               y: this._anglePoints[3],
//             };

//             console.log(CounterclockwiseRotationAngle, ' CounterclockwiseRotationAngle');
//             const textP = this.rotatePoint(APoint.x, APoint.y, BPoint.x, BPoint.y, -90);
//             const textX = textP.x;
//             const textY = textP.y;

//             const testLine = new Konva.Line({
//               points: [APoint.x, APoint.y, BPoint.x, BPoint.y, textX, textY],
//               lineCap: 'round',
//               lineJoin: 'round',
//               name: 'angleLine',
//               stroke: 'green',
//               strokeWidth: 1,
//               hitStrokeWidth: 30,
//             });
//             (this._node as Group).add(testLine);

//             const text = degree > 180 ? (360 - degree).toFixed(1) + '' : degree.toFixed(1) + '';
//             const angleText = new Konva.Text({
//               x: textX,
//               y: textY,
//               text: text,
//               fontSize: 20,
//               fill: 'red',
//             });
//             (this._node as Group)?.add(angleText);
//             this._anglePoints = [];
//             const angleLine = (this._node as Group).getChildren?.((node) => {
//               return node.attrs.name === 'angleLine';
//             })[0] as Line;
//             angleLine?.setAttrs({
//               dash: [],
//             });
//             return this.end();
//           }

//           if (!this._node) {
//             this._node = new Konva.Group({
//               name: DrawType.angle,
//               strokeScaleEnabled: false,
//             });
//             const line = new Konva.Line({
//               ...angleConfig.config,
//               points: this._anglePoints,
//               lineCap: 'round',
//               lineJoin: 'round',
//               name: 'angleLine',
//               strokeScaleEnabled: false,
//               hitStrokeWidth: 30,
//             });
//             this._node.add(line);
//             this._layer.add(this._node);
//             this.setNodeId();

//             return this._node;
//           }
//         }
//       },
//     };
//     const handler = handlers[angleConfig.handler];
//     if (handler) handler();
//   }

//   // 折线
//   _foleLinePoints: number[] = [];
//   _mouseupFoleLinePoints: number[] = [];
//   drawFoldLine(foldLineConfig: any) {
//     this.start();
//     const { ev } = foldLineConfig;
//     const curPoint = this.getCurPoint();
//     const handlers: { [key: string]: () => void } = {
//       contextmenu: () => {
//         // 右键
//         ev.evt.preventDefault();
//         if (this._mouseupFoleLinePoints.length <= 2) {
//           this._nodeDestroy();
//         } else {
//           this._node?.setAttrs({
//             points: this._mouseupFoleLinePoints,
//           });
//         }
//         this._status = DrawStatus.drawingCompleted;
//         this._foleLinePoints = [];
//         this._mouseupFoleLinePoints = [];
//         return this.end();
//       },
//       mousedown: () => {},
//       mousemove: () => {
//         if (this._node) {
//           this._node?.setAttrs({
//             points: [...this._foleLinePoints, curPoint.x, curPoint.y],
//           });
//         }
//       },
//       mouseup: () => {
//         const button = ev.evt.button;
//         if (isMouseLeftOrTouch(ev)) {
//           // 鼠标左键
//           ev.evt.stopPropagation();
//           ev.evt.cancelBubble = true;
//           this._foleLinePoints = [...this._foleLinePoints, curPoint.x, curPoint.y];
//           this._mouseupFoleLinePoints = [...this._mouseupFoleLinePoints, curPoint.x, curPoint.y];
//           // 处理双击退出
//           if (this._mouseupFoleLinePoints.length === 4) {
//             const { width, height } = this.getRect(
//               {
//                 x: this._mouseupFoleLinePoints[0],
//                 y: this._mouseupFoleLinePoints[1],
//               },
//               curPoint
//             );
//             if (width < this._minWidth || height < this._minHeight) {
//               this._nodeDestroy();
//               this._node = null;
//               this._status = DrawStatus.drawingCompleted;
//               this._foleLinePoints = [];
//               this._mouseupFoleLinePoints = [];
//               return;
//             }
//           }

//           if (!this._node) {
//             this._node = new Konva.Line({
//               ...foldLineConfig.config,
//               points: this._foleLinePoints,
//               lineCap: 'round',
//               lineJoin: 'round',
//               name: DrawType.foldLine,
//               strokeScaleEnabled: false,
//               zoom: this._viewer.viewport.getZoom(),
//               hitStrokeWidth: 30,
//             });
//             this._layer.add(this._node);
//             this.setNodeId();
//             return this._node;
//           }
//         }
//       },
//       dblclick: () => {
//         console.log('dblclick');
//         // this._status = DrawStatus.drawingCompleted;
//       },
//     };
//     const handler = handlers[foldLineConfig.handler];
//     if (handler) handler();
//   }

//   // 多边形
//   _polygonPoints: any[] = [];
//   drawPolygon(polygonConfig: any) {
//     this.start();
//     const { ev } = polygonConfig;
//     const curPoint = this.getCurPoint();
//     const handlers: { [key: string]: () => void } = {
//       contextmenu: () => {
//         ev.evt.preventDefault();
//         if (this._mouseupFoleLinePoints.length <= 4) {
//           this._node?.setAttrs({
//             points: [...this._mouseupFoleLinePoints, curPoint.x, curPoint.y],
//             closed: true,
//           });
//         } else {
//           this._node?.setAttrs({
//             points: this._mouseupFoleLinePoints,
//             closed: true,
//           });
//         }

//         this._status = DrawStatus.drawingCompleted;
//         this._polygonPoints = [];
//         this._mouseupFoleLinePoints = [];
//         return this.end();
//       },
//       mousedown: () => {},
//       mousemove: () => {
//         if (this._node) {
//           this._node?.setAttrs({
//             points: [...this._polygonPoints, curPoint.x, curPoint.y],
//             // closed: false
//             hitStrokeWidth: 5,
//           });
//         }
//       },
//       mouseup: () => {
//         console.log('up');
//         const button = ev.evt.button;
//         if (isMouseLeftOrTouch(ev)) {
//           // 鼠标左键
//           ev.evt.stopPropagation();
//           ev.evt.cancelBubble = true;

//           this._polygonPoints = [...this._polygonPoints, curPoint.x, curPoint.y];
//           this._mouseupFoleLinePoints = [...this._mouseupFoleLinePoints, curPoint.x, curPoint.y];

//           if (this._polygonPoints.length === 4) {
//             // 双击退出的处理
//             const { width, height } = this.getRect(
//               {
//                 x: this._mouseupFoleLinePoints[0],
//                 y: this._mouseupFoleLinePoints[1],
//               },
//               curPoint
//             );
//             if (width < this._minWidth || height < this._minHeight) {
//               this._nodeDestroy();
//               this._node = null;
//               this._status = DrawStatus.drawingCompleted;
//               this._polygonPoints = [];
//               this._mouseupFoleLinePoints = [];
//               return;
//             }
//           }

//           if (this._polygonPoints.length >= 4) {
//             // 判断是否在第一个点附近
//             const startPoint = this._polygonPoints.slice(0, 2);
//             const offset = 2;
//             if (
//               (curPoint.x > startPoint[0] - offset && curPoint.x < startPoint[0] + offset) ||
//               (curPoint.y > startPoint[1] - offset && curPoint.y < startPoint[1] + offset)
//             ) {
//               if (this._node) {
//                 // 确保末尾点等于初始点
//                 this._node?.setAttrs({
//                   points: [...this._polygonPoints.slice(0, -2), ...startPoint],
//                 });
//               }
//               this._status = DrawStatus.drawingCompleted;
//               this._polygonPoints = [];
//               this._mouseupFoleLinePoints = [];
//               this.end();
//             }
//           }

//           if (!this._node) {
//             this._node = new Konva.Line({
//               ...polygonConfig.config,
//               points: this._polygonPoints,
//               lineCap: 'round',
//               lineJoin: 'round',
//               closed: false,
//               name: DrawType.polygon,
//               strokeScaleEnabled: false,
//               zoom: this._viewer.viewport.getZoom(),
//               hitStrokeWidth: 30,
//             });
//             this._layer.add(this._node);
//             this.setNodeId();
//             return this._node;
//           }
//         }
//       },
//     };
//     const handler = handlers[polygonConfig.handler];
//     if (handler) handler();
//   }

//   _freeCurvePoints: any[] = [];
//   drawFreeCurve(freeCurveConfig: any) {
//     const { ev } = freeCurveConfig;
//     this.start();

//     const curPoint = this.getCurPoint();
//     const { width, height } = this.getRect(this._startPoint, curPoint);
//     const handlers: { [key: string]: () => void } = {
//       contextmenu: () => {
//         // ev.evt.preventDefault();
//         // this._node?.setAttrs({
//         //   points: this._freeCurvePoints,
//         // });
//         // this._freeCurvePoints = [];
//         // return this.end();
//       },
//       mousedown: () => {
//         this._startPoint = curPoint;
//         this._freeCurvePoints = [...this._freeCurvePoints, curPoint.x, curPoint.y];
//         if (!this._node) {
//           this._node = new Konva.Line({
//             ...freeCurveConfig.config,
//             points: this._freeCurvePoints,
//             lineCap: 'round',
//             lineJoin: 'round',
//             name: DrawType.freeCurve,
//             strokeScaleEnabled: false,
//             zoom: this._viewer.viewport.getZoom(),
//             hitStrokeWidth: 30,
//           });
//           this._layer.add(this._node);
//           this.setNodeId();
//           return this._node;
//         }
//       },
//       mousemove: () => {
//         if (this._node) {
//           this._freeCurvePoints = [...this._freeCurvePoints, curPoint.x, curPoint.y];
//           this._node?.setAttrs({
//             points: this._freeCurvePoints,
//             tension: 1,
//           });
//         }
//       },
//       mouseup: () => {
//         const button = ev.evt.button;
//         if (isMouseLeftOrTouch(ev)) {
//           // 鼠标左键
//           if (width < this._minWidth || height < this._minHeight) {
//             this._nodeDestroy();
//           }
//           this.end();
//           this._freeCurvePoints = [];
//           ev.evt.stopPropagation();
//           ev.evt.cancelBubble = true;
//         }
//       },
//     };
//     const handler = handlers[freeCurveConfig.handler];
//     if (handler) handler();
//   }

//   _closeCurvePoints: any[] = [];
//   drawClosedCurve(closedCurveldLineConfig: any) {
//     this.start();
//     const { ev } = closedCurveldLineConfig;
//     const curPoint = this.getCurPoint();
//     const { width, height } = this.getRect(this._startPoint, curPoint);
//     const handlers: { [key: string]: () => void } = {
//       contextmenu: () => {
//         // ev.evt.preventDefault();
//         // this._node?.setAttrs({
//         //   points: this._closeCurvePoints,
//         // });
//         // this._closeCurvePoints = [];
//         // return this.end();
//       },
//       mousedown: () => {
//         this._startPoint = curPoint;
//         this._closeCurvePoints = [...this._closeCurvePoints, curPoint.x, curPoint.y];
//         if (!this._node) {
//           this._node = new Konva.Line({
//             ...closedCurveldLineConfig.config,
//             points: this._closeCurvePoints,
//             lineCap: 'round',
//             lineJoin: 'round',
//             name: DrawType.closedCurve,
//             strokeScaleEnabled: false,
//             zoom: this._viewer.viewport.getZoom(),
//             hitStrokeWidth: 30,
//           });
//           this._layer.add(this._node);
//           this.setNodeId();
//           return this._node;
//         }
//       },
//       mousemove: () => {
//         if (this._node) {
//           this._closeCurvePoints = [...this._closeCurvePoints, curPoint.x, curPoint.y];
//           this._node?.setAttrs({
//             points: this._closeCurvePoints,
//             tension: 1,
//           });
//         }
//       },
//       mouseup: () => {
//         const button = ev.evt.button;
//         if (isMouseLeftOrTouch(ev)) {
//           // 鼠标左键

//           if (this._node) {
//             this._closeCurvePoints = [...this._closeCurvePoints, curPoint.x, curPoint.y];
//             this._node?.setAttrs({
//               points: this._closeCurvePoints,
//               closed: true,
//             });
//           }
//           if (width < this._minWidth || height < this._minHeight) {
//             this._nodeDestroy();
//           }
//           this.end();
//           this._closeCurvePoints = [];
//           ev.evt.stopPropagation();
//           ev.evt.cancelBubble = true;
//         }
//       },
//     };
//     const handler = handlers[closedCurveldLineConfig.handler];
//     if (handler) handler();
//   }

//   _ring: Konva.Ring | null = null;
//   _innerCircle: Konva.Circle | null = null;
//   _outerCircle: Konva.Circle | null = null;
//   drawRing(ringConfig: any) {
//     this.start();
//     const { ev } = ringConfig;
//     const curPoint = this.getCurPoint();
//     const { width, height } = this.getRect(this._startPoint, curPoint);
//     const handlers: { [key: string]: () => void } = {
//       contextmenu: () => {
//         // ev.evt.preventDefault();
//         // this._closeCurvePoints = [];
//         // return this.end();
//       },
//       mousedown: () => {
//         if (!this._node) {
//           this._startPoint = curPoint;
//           this._node = new Konva.Group({
//             name: DrawType.ring,
//           });
//           // const center = new Konva.Circle({
//           //   x: this._startPoint.x,
//           //   y: this._startPoint.y,
//           //   fill: '#FFFF00',
//           //   stroke: '#FFFF00',
//           //   // strokeWidth: 2,
//           //   radius: 1,
//           //   // fill: 'red',
//           //   // stroke: 'black',
//           //   strokeWidth: 2,
//           // });
//           // this._node.add(center);
//           this._layer.add(this._node);
//           this.setNodeId();
//         }

//         // this._startPoint = curPoint;
//         // this._closeCurvePoints = [...this._closeCurvePoints, curPoint.x, curPoint.y];
//         // if (!this._node) {
//         //   this._node = new Konva.Line({
//         //     ...ringConfig.config,
//         //     points: this._closeCurvePoints,
//         //     lineCap: 'round',
//         //     lineJoin: 'round',
//         //     name: 'angle',
//         // strokeScaleEnabled: false,
//         // hitStrokeWidth: 30,
//         //   });
//         //   this._layer.add(this._node);
//         //   this.setNodeId();
//         //   return this._node;
//         // }
//       },
//       mousemove: () => {
//         const dx = Math.abs(curPoint.x - this._startPoint.x);
//         const dy = Math.abs(curPoint.y - this._startPoint.y);
//         const radius = (dx ** 2 + dy ** 2) ** (1 / 2);
//         if (!this._ring) {
//           this._ring = new Konva.Ring({
//             x: this._startPoint.x,
//             y: this._startPoint.y,
//             innerRadius: radius / 2,
//             outerRadius: radius,
//             stroke: 'red',
//             strokeWidth: 2,
//             name: 'GroupRing',
//             zoom: this._viewer.viewport.getZoom(),
//           });
//           (this._node as Group).add(this._ring);
//         } else {
//           this._ring.setAttrs({
//             x: this._startPoint.x,
//             y: this._startPoint.y,
//             innerRadius: radius / 2,
//             outerRadius: radius,
//           });
//         }
//         // 外圆
//         // if (!this._outerCircle) {
//         //   this._outerCircle = new Konva.Circle({
//         //     x: curPoint.x,
//         //     y: curPoint.y,
//         //     fill: '#FFFF00',
//         //     stroke: '#FFFF00',
//         //     radius: 1,
//         //     strokeWidth: 2,
//         //     name: 'outerCircle'
//         //   });
//         //   (this._node as Group).add(this._outerCircle);
//         // } else {
//         //   this._outerCircle.setAttrs({
//         //     x: curPoint.x,
//         //     y: curPoint.y,
//         //   });
//         // }
//         // 内圆
//         // const innerCircleX = (curPoint.x - this._startPoint.x) / 2 + this._startPoint.x;
//         // const innerCircleY = (curPoint.y - this._startPoint.y) / 2 + this._startPoint.y;
//         // if (!this._innerCircle) {
//         //   this._innerCircle = new Konva.Circle({
//         //     x: innerCircleX,
//         //     y: innerCircleY,
//         //     fill: '#FFFF00',
//         //     stroke: '#FFFF00',
//         //     radius: 1,
//         //     strokeWidth: 2,
//         //     name: 'innerCircle'
//         //   });
//         //   (this._node as Group).add(this._innerCircle);
//         // } else {
//         //   this._innerCircle.setAttrs({
//         //     x: innerCircleX,
//         //     y: innerCircleY,
//         //   });
//         // }
//       },
//       mouseup: () => {
//         const button = ev.evt.button;
//         if (isMouseLeftOrTouch(ev)) {
//           // 鼠标左键
//           if (this._node) {
//             // this._closeCurvePoints = [...this._closeCurvePoints, curPoint.x, curPoint.y];
//             // this._node?.setAttrs({
//             //   points: this._closeCurvePoints,
//             //   closed: true,
//             // });
//           }
//           // if (width < this._minWidth || height < this._minHeight) {
//           //   this._nodeDestroy()
//           // }
//           this.end();
//           this._ring = null;
//           this._outerCircle = null;
//           this._innerCircle = null;
//           // this._closeCurvePoints = [];
//           // ev.evt.stopPropagation();
//           // ev.evt.cancelBubble = true;
//         }
//       },
//     };
//     const handler = handlers[ringConfig.handler];
//     if (handler) handler();
//   }

//   /**
//    * 处理shape太小的问题，直接销毁
//    */
//   _nodeDestroy() {
//     if (this._node) {
//       this._node.remove();
//       this._node.destroy();
//       this._layer.draw();
//       this._node = null;
//       this._startPoint = {};
//       this._foleLinePoints = [];
//       this._mouseupFoleLinePoints = [];
//       this._polygonPoints = [];
//       this._freeCurvePoints = [];
//       this._closeCurvePoints = [];
//     }
//   }
//   // 鼠标在浏览器的坐标转为KONVA的坐标点
//   konvaPointFromPixl(x: number, y: number) {
//     //
//     var viewportPoint = this._viewer.viewport.pointFromPixel(new Point(x, y));
//     var imagePoint = this._viewer.viewport.viewportToImageCoordinates(viewportPoint);

//     // 左上坐标
//     var pixelNoRotate = this._viewer.viewport.pixelFromPointNoRotate(
//       new OpenSeadragon.Point(0, 0),
//       true
//     );
//     const point = this._viewer.viewport.pointFromPixel(new Point(x, y));
//     var pixel = this._viewer.viewport.pixelFromPointNoRotate(point); //当前坐标转化为旋转度数为0度的实际坐标
//     return {
//       x: (pixel.x - pixelNoRotate.x) / this.layerScale,
//       y: (pixel.y - pixelNoRotate.y) / this.layerScale,
//       imageX: imagePoint.x,
//       imageY: imagePoint.y,
//     };
//   }

//   //获取顶点和宽高  xy是startpoint endpoint里取最小值？
//   getRect(
//     startPoint: DrawPoint,
//     endPoint: DrawPoint
//   ): { x: number; y: number; width: number; height: number } {
//     const width = Math.abs(startPoint.x - endPoint.x);
//     const height = Math.abs(startPoint.y - endPoint.y);
//     const minX = Math.min(startPoint.x, endPoint.x);
//     const minY = Math.min(startPoint.y, endPoint.y);
//     const x = endPoint.x;
//     const y = endPoint.y;
//     if (this._drawType === DrawType.flag) {
//       return {
//         x,
//         y,
//         width,
//         height,
//       };
//     }
//     return {
//       x: minX,
//       y: minY,
//       width,
//       height,
//     };
//   }

//   // 开始绘画
//   start() {
//     console.log('go start');
//     this._status = DrawStatus.drawing;
//   }

//   // 结束绘画
//   end() {
//     this._status = DrawStatus.end;
//     if (this._node) {
//       this._node.setAttrs({
//         dash: [],
//       });
//       console.log('go end');
//       this._drawEndCallback?.(this._node, this._layer);
//       this.updateHistory();
//     }
//     this._node = null;
//     this._startPoint = {};
//   }

//   selectCallback(cb: Function) {
//     this._selectCallback = cb;
//   }
//   cancelSelectCallback(cb: Function) {
//     this._cancelSelect = cb;
//   }

//   select(node: any) {
//     this.cancelSelect();
//     this._status = DrawStatus.select;
//     this._transformer = new Konva.Transformer({
//       node: node,
//       // centeredScaling: true,
//     });
//     this._node = node;
//     this._node?.setAttrs({
//       draggable: true,
//       active: true,
//     });
//     this._layer.add(this._transformer);
//     this._selectCallback?.(node);
//     // this._drawEndCallback?.(this._node, this._layer);
//   }

//   cancelSelect() {
//     //
//     if (this._status === DrawStatus.select) {
//       if (this._transformer) {
//         this._transformer.destroy();
//       }
//       this._node?.setAttrs({
//         draggable: false,
//         active: false,
//       });

//       this._node = null;
//     }
//     this._node?.setAttrs({
//       draggable: false,
//     });
//     this._status = DrawStatus.end;
//     this._cancelSelect?.();

//     // 不能为null的原因是双击的时候node为null就不能摧毁 会导致layer多一个node
//     // this._node = null;
//     // 要设为null的原因是因为选中之后再点击stage会导致layer多一个node
//   }

//   deleteNode() {
//     this._transformer.destroy();
//     this._nodeDestroy();
//     this._node = null;
//     this._status = DrawStatus.end;
//   }
// }
