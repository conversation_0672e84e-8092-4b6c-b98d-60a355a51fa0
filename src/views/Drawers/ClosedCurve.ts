import { BaseDrawer } from './BaseDrawer';
import { Line, LineConfig } from 'konva/lib/shapes/Line';
import OpenSeadragon from 'openseadragon';
import { Layer } from 'konva/lib/Layer';
import { DrawType, DrawStatus } from '@/views/Marker/components/editorEnum';
import { isMouseLeftOrTouch } from '@/utils/index';
import { Context } from 'konva/lib/Context';
export class ClosedCurveDrawer extends BaseDrawer<Line> {
  private _ClosedCurveConfig: any;
  private _closedCurvePoints: number[] = [];
  private _closedCurveImagePoints: number[] = [];
  _ratio: any = {
    unit: 'px',
    scales: 1,
  };
  constructor(layer: Layer, viewer: OpenSeadragon.Viewer, closedCurveConfig: any) {
    super(layer, viewer, closedCurveConfig);
    this._ClosedCurveConfig = closedCurveConfig;
  }

  override drawContextmenu(): void {}

  override drawDown() {
    this.start();
    this.startPoint = this.curPoint;
    this._closedCurvePoints = [...this._closedCurvePoints, this.curPoint.x, this.curPoint.y];
    this._closedCurveImagePoints = [
      ...this._closedCurveImagePoints,
      this.curPoint.imageX,
      this.curPoint.imageY,
    ];
    if (!this.node) {
      this.node = this.createNode();
      this.layer.add(this.node);
      this.setNodeId();
      return this.node;
    }
  }

  override drawMove() {
    if (this.node) {
      this._closedCurvePoints = [...this._closedCurvePoints, this.curPoint.x, this.curPoint.y];
      this._closedCurveImagePoints = [
        ...this._closedCurveImagePoints,
        this.curPoint.imageX,
        this.curPoint.imageY,
      ];
      this.node?.setAttrs({
        points: this._closedCurvePoints,
        imagePoints: this._closedCurveImagePoints,
        tension: 1,
        pixelCoords: {
          x: this.startPoint.imageX,
          y: this.startPoint.imageY,
          imagePoints: this._closedCurveImagePoints,
        },
      });
    }
  }

  override drawUp() {
    const ev = this._ClosedCurveConfig.ev;
    const { width, height } = this.getRect(this.startPoint, this.curPoint);

    if (isMouseLeftOrTouch(ev)) {
      // 鼠标左键

      if (this.node) {
        this._closedCurvePoints = [...this._closedCurvePoints, this.curPoint.x, this.curPoint.y];
        this._closedCurveImagePoints = [
          ...this._closedCurveImagePoints,
          this.curPoint.imageX,
          this.curPoint.imageY,
        ];
        this.node?.setAttrs({
          points: this._closedCurvePoints,
          imagePoints: this._closedCurveImagePoints,
          closed: true,
          pixelCoords: {
            x: this.startPoint.imageX,
            y: this.startPoint.imageY,
            imagePoints: this._closedCurveImagePoints,
          },
        });
      }
      if (
        width < this.minWidth ||
        (height < this.minHeight && this._closedCurvePoints.length < 4)
      ) {
        this.nodeDestroy();
      }
      this.end();
      this._closedCurvePoints = [];
      this._closedCurveImagePoints = [];
      ev.evt.stopPropagation();
      ev.evt.cancelBubble = true;
    }
  }

  createNode() {
    const node = new Line({
      ...this._ClosedCurveConfig.config,
      points: this._closedCurvePoints,
      imagePoints: this._closedCurveImagePoints,
      lineCap: 'round',
      lineJoin: 'round',
      name: DrawType.closedCurve,
      strokeScaleEnabled: false,
      zoom: this.viewer.viewport.getZoom(),
      hitStrokeWidth: 30,
    });
    node.on('dragmove dragend transform transformend', (e: any) => {
      const originalPoints = node.getAttrs().points;
      const transform = node.getAbsoluteTransform();
      const resultPoints = [];
      for (let i = 0; i < originalPoints.length; i += 2) {
        const x = originalPoints[i];
        const y = originalPoints[i + 1];
        const pt = transform.point({ x, y });
        const resultPoint = this.viewCoordinatesToImageCoordinates(this.viewer, pt.x, pt.y);
        resultPoints.push(resultPoint.imageX, resultPoint.imageY);
      }
      node?.setAttrs({
        pixelCoords: {
          x: resultPoints[0],
          y: resultPoints[1],
          imagePoints: resultPoints,
        },
      });
    });
    if (this._ClosedCurveConfig.needCountArea) {
      node.setAttr('sceneFunc', (con: Context, shape: any) => {
        con.beginPath();
        for (let i = 2; i < shape.attrs.points.length; i += 2) {
          con.lineTo(shape.attrs.points[i], shape.attrs.points[i + 1]);
        }
        con.closePath();
        con.fillStrokeShape(shape as any);
        con.fillStyle = shape.attrs.stroke;
        con.scale(
          Math.min(1, 2 / this.layerScale) / shape.scaleX(),
          Math.min(1, 2 / this.layerScale) / shape.scaleY()
        );
        //当前处于绘制或选中当前节点
        if (this.selectNode?.attrs?.id === node.attrs?.id || this.status !== DrawStatus.end) {
          if (!node.attrs?.pixelCoords?.imagePoints) return;
          const Description = this.closedAreaPerimeter(
            node.attrs?.pixelCoords?.imagePoints,
            {
              scaleX: 1,
              scaleY: 1,
            },
            this._ratio
          );
          con.translate(
            (node.attrs?.pixelCoords?.imagePoints[0] / Math.min(1, 2 / this.layerScale)) *
              shape.scaleX(),
            (node.attrs?.pixelCoords?.imagePoints[1] / Math.min(1, 2 / this.layerScale)) *
              shape.scaleY()
          );
          con.fillText(Description, 0, -shape.attrs.strokeWidth);
          //选中的节点保存移动变化的数据
          // if(this.status === DrawStatus.select) {
          node?.setAttrs({
            points: shape.points(),
            tension: 1,
            Description,
            scaleRuler: shape.attrs.scaleRuler || 1,
          });
          // }
        } else {
          con.translate(
            (shape.points()[0] / Math.min(1, 2 / this.layerScale)) * shape.scaleX(),
            (shape.points()[1] / Math.min(1, 2 / this.layerScale)) * shape.scaleY()
          );
          con.fillText(shape.attrs.Description || '', 0, -shape.attrs.strokeWidth);
        }
      });
    }
    return node;
  }
}
