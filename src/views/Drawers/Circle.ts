import { BaseDrawer } from './BaseDrawer';
import Konva from 'konva';
import OpenSeadragon, { Point } from 'openseadragon';
import { Layer } from 'konva/lib/Layer';
import { DrawType, DrawStatus } from '@/views/Marker/components/editorEnum';
import { isMouseLeftOrTouch } from '@/utils/index';
import { Context } from 'konva/lib/Context';
export class CircleDrawer extends BaseDrawer<Konva.Circle> {
  private _CircleConfig: any;

  constructor(layer: Layer, viewer: OpenSeadragon.Viewer, circleConfig: any) {
    super(layer, viewer, circleConfig);
    this._CircleConfig = circleConfig;
  }

  override drawDown() {
    this.start();
    this.startPoint = this.curPoint;
    this.node = this.createNode();
    this.layer.add(this.node);
    this.setNodeId();
  }

  override drawMove() {
    const dx = Math.abs(this.curPoint.x - this.startPoint.x);
    const dy = Math.abs(this.curPoint.y - this.startPoint.y);
    const radius = (dx ** 2 + dy ** 2) ** (1 / 2);
    const distanceX = Math.abs(this.curPoint.imageX - this.startPoint.imageX);
    const distanceY = Math.abs(this.curPoint.imageY - this.startPoint.imageY);
    const imageR = (distanceX ** 2 + distanceY ** 2) ** (1 / 2);
    this.node?.setAttrs({
      radius,
      pixelCoords: {
        x: this.startPoint.imageX - imageR,
        y: this.startPoint.imageY - imageR,
        centerX: this.startPoint.imageX,
        centerY: this.startPoint.imageY,
        radiusX: imageR,
        radiusY: imageR,
        imagePoints: [
          this.startPoint.imageX - imageR,
          this.startPoint.imageY - imageR,
          this.startPoint.imageX + imageR,
          this.startPoint.imageY + imageR,
        ],
      },
    });
    console.log(this.node, 'this._node');
  }

  override drawUp() {
    if (isMouseLeftOrTouch(this._CircleConfig.ev)) {
      const { width, height } = this.getRect(this.startPoint, this.curPoint);
      // 鼠标左键
      if (width < this.minWidth || height < this.minHeight) {
        this.nodeDestroy();
      }
      this.status = DrawStatus.drawingCompleted;
      this.end();
    }
  }

  // 创建node 抽离方便后续addmarker调用
  createNode() {
    const { x, y } = this.getRect(this.startPoint, this.curPoint);
    const node = new Konva.Circle({
      ...this._CircleConfig.config,
      x: x,
      y: y,
      name: DrawType.circle,
      strokeScaleEnabled: false,
      zoom: this.viewer.viewport.getZoom(),
    });
    node.on('dragmove dragend transform transformend', (e: any) => {
      const { x, y, width, height } = node.getClientRect({
        skipStroke: true,
        skipShadow: true,
      });
      const { scaleX, scaleY } = node.attrs;
      const startPoint = this.viewCoordinatesToImageCoordinates(this.viewer, x, y);
      const curPoint = this.viewCoordinatesToImageCoordinates(this.viewer, x + width, y + height);
      const imageRadiusX = (curPoint.imageX - startPoint.imageX) / 2;
      const imageRadiusY = (curPoint.imageY - startPoint.imageY) / 2;
      const imageCenterX = startPoint.imageX + imageRadiusX;
      const imageCenterY = startPoint.imageY + imageRadiusY;
      console.log(x, y, width, height, imageRadiusX, imageRadiusY, 'imageRadiusX');
      node.setAttrs({
        pixelCoords: {
          x: startPoint.imageX,
          y: startPoint.imageY,
          centerX: imageCenterX,
          centerY: imageCenterY,
          radiusX: imageRadiusX,
          radiusY: imageRadiusY,
          imagePoints: [startPoint.imageX, startPoint.imageY, curPoint.imageX, curPoint.imageY],
        },
      });
    });
    if (this._CircleConfig.needCountArea) {
      node.setAttr('sceneFunc', (con: Context, shape: any) => {
        con.beginPath();
        con.lineWidth = 1 /this.layerScale;
        con.arc(0, 0, shape.attrs.radius, 0, Math.PI * 2, false);
        con.closePath();
        con.fillStrokeShape(shape as any);
        con.fillStyle = shape.attrs.stroke;
        con.scale(
          Math.min(1, 2 / this.layerScale) / shape.scaleX(),
          Math.min(1, 2 / this.layerScale) / shape.scaleY()
        );
        const { pixelCoords } = node.attrs;
        if (this.selectNode?.attrs?.id === node.attrs?.id || this.status !== DrawStatus.end) {
          if (!node.attrs?.pixelCoords?.radiusX) return;
          const Description = this.circleAreaAndPerimeter(
            pixelCoords?.imagePoints[2] - pixelCoords?.imagePoints[0],
            pixelCoords?.imagePoints[3] - pixelCoords?.imagePoints[1]
          );
          // console.log(node.attrs.pixelCoords.radiusX, Description, this._ratio)
          con.translate(
            0,
            -((shape.attrs.radius / Math.min(1, 2 / this.layerScale))) * shape.scaleY()
          );
          con.fillText(Description, 0, -shape.attrs.strokeWidth);

          node?.setAttrs({
            Description,
          });
        } else {
          con.translate(
            0,
            -((shape.attrs.radius / Math.min(1, 2 / this.layerScale)))* shape.scaleY()
          );
          // console.log(shape.attrs.Description, 'shape.attrs.Description', this.selectNode, node)
          con.fillText(shape.attrs.Description || '', 0, -shape.attrs.strokeWidth );
        }
      });
    }

    return node;
  }

  /**
   * 计算圆形面积和周长
   */
  circleAreaAndPerimeter:(x: number, y: number) => string = (x, y) => {
    const w = (x * this._ratio.scales) / 2;
    const h = (y * this._ratio.scales) / 2;
    return (
      (Math.PI * w * h).toFixed(2) +
      this._ratio.unit +
      '² ' +
      (2 * Math.PI * Math.min(w, h) + 4 * Math.abs(w - h)).toFixed(2) +
      this._ratio.unit
    );
  };
}
