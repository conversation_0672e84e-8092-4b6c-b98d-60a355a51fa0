import { BaseDrawer } from './BaseDrawer';
import { Arrow, ArrowConfig } from 'konva/lib/shapes/Arrow';
import OpenSeadragon, { Point } from 'openseadragon';
import { Layer } from 'konva/lib/Layer';
import { DrawType, DrawStatus } from '@/views/Marker/components/editorEnum';
import { changeArrowPointerByZoom, isMouseLeftOrTouch } from '@/utils/index';

export class ArrowDrawer extends BaseDrawer<Arrow> {
  private _ArrowConfig: any;

  constructor(layer: Layer, viewer: OpenSeadragon.Viewer, arrowConfig: any) {
    super(layer, viewer, arrowConfig);
    this._ArrowConfig = arrowConfig;
  }

  //   处理需要的数据
  private _handleResultData() {
    const { x, y, width, height } = this.getRect(this.startPoint, this.curPoint);
    const drawArrow = [
      this.startPoint.x,
      this.startPoint.y,
      width,
      height,
      this.curPoint.x,
      this.curPoint.y,
    ];
    let resultWidth = 0;
    let resultHeight = 0;
    if (drawArrow[0] > drawArrow[4] && drawArrow[1] > drawArrow[5]) {
      resultWidth = -drawArrow[2];
      resultHeight = -drawArrow[3];
    } else if (drawArrow[0] > drawArrow[4] && drawArrow[1] < drawArrow[5]) {
      resultWidth = -drawArrow[2];
      resultHeight = drawArrow[3];
    } else if (drawArrow[0] < drawArrow[4] && drawArrow[1] > drawArrow[5]) {
      resultWidth = drawArrow[2];
      resultHeight = -drawArrow[3];
    } else if (drawArrow[0] < drawArrow[4] && drawArrow[1] < drawArrow[5]) {
      resultWidth = drawArrow[2];
      resultHeight = drawArrow[3];
    }
    return {
      drawArrow,
      resultWidth,
      resultHeight,
    };
  }

  override drawDown() {
    if (!this.node) {
      this.start();
      this.startPoint = this.curPoint;
      this.node = this.createNode();
      changeArrowPointerByZoom(this.node as Arrow, this.viewer.viewport.getZoom());
      this.node.setAttr('fill', this.node.getAttr('stroke'));
      this.layer.add(this.node);
      this.setNodeId();
    }
  }

  override drawMove() {
    const { drawArrow, resultWidth, resultHeight } = this._handleResultData();
    const curPoint = this.getCurPoint();
    this.node?.setAttrs({
      x: drawArrow[0],
      y: drawArrow[1],
      points: [0, 0, resultWidth, resultHeight],
      hitStrokeWidth: 30,
      strokeWidth: this._ArrowConfig.config.strokeWidth,
      pixelCoords: {
        x: this.startPoint.imageX,
        y: this.startPoint.imageY,
        imagePoints: [
          this.startPoint.imageX,
          this.startPoint.imageY,
          curPoint.imageX,
          curPoint.imageY,
        ],
      },
    });
  }

  override drawUp() {
    if (isMouseLeftOrTouch(this._ArrowConfig.ev)) {
      const { width, height } = this.getRect(this.startPoint, this.curPoint);
      // 鼠标左键
      if (width < this.minWidth || height < this.minHeight) {
        this.nodeDestroy();
      }
      this.status = DrawStatus.drawingCompleted;
      this.end();
    }
  }

  createNode() {
    const { resultWidth, resultHeight } = this._handleResultData();
    const node = new Arrow({
      ...this._ArrowConfig.config,
      x: this.curPoint.x,
      y: this.curPoint.y,
      points: [0, 0, resultWidth, resultHeight],
      name: DrawType.arrow,
      strokeScaleEnabled: false,
      zoom: this.viewer.viewport.getZoom(),
      strokeWidth: 0,
    });
    node.on('dragmove dragend transform transformend', (e: any) => {
      const { x, y, width, height } = node.getClientRect({
        skipStroke: true,
        skipShadow: true
      });
      const { points } = node.getAttrs();
      // 拿来判断箭头反向
      const pointsX = points[2];
      const pointsY = points[3];
      const leftTopPoint = this.viewCoordinatesToImageCoordinates(this.viewer, x, y);
      const leftBottomPoint = this.viewCoordinatesToImageCoordinates(this.viewer, x, y + height);
      const rightTopPoint = this.viewCoordinatesToImageCoordinates(this.viewer, x + width, y);
      const rightBottomPoint = this.viewCoordinatesToImageCoordinates(
        this.viewer,
        x + width,
        y + height
      );
      let startPoint = { imageX: 0, imageY: 0 };
      let curPoint = { imageX: 0, imageY: 0 };
      // debugger;
      if (pointsX < 0 && pointsY < 0) {
        // 指向左上角
        startPoint = rightBottomPoint;
        curPoint = leftTopPoint;
      } else if (pointsX > 0 && pointsY < 0) {
        // 指向右上角
        startPoint = leftBottomPoint;
        curPoint = rightTopPoint;
      } else if (pointsX < 0 && pointsY > 0) {
        // 指向左下角
        startPoint = rightTopPoint;
        curPoint = leftBottomPoint;
      } else if (pointsX > 0 && pointsY > 0) {
        // 指向右下角
        startPoint = leftTopPoint;
        curPoint = rightBottomPoint;
      } else {
        startPoint = { imageX: 0, imageY: 0 };
        curPoint = { imageX: 0, imageY: 0 };
      }
      node.setAttrs({
        pixelCoords: {
          x: leftTopPoint.imageX,
          y: leftTopPoint.imageY,
          imagePoints: [startPoint.imageX, startPoint.imageY, curPoint.imageX, curPoint.imageY],
        },
      });
    });
    return node;
  }
}
