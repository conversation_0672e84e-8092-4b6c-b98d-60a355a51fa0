import { BaseDrawer } from './BaseDrawer';
import { Line, LineConfig } from 'konva/lib/shapes/Line';
import OpenSeadragon from 'openseadragon';
import { Layer } from 'konva/lib/Layer';
import { DrawType, DrawStatus } from '@/views/Marker/components/editorEnum';
import { isMouseLeftOrTouch } from '@/utils/index';
import { Context } from 'konva/lib/Context';
export class PolygonDrawer extends BaseDrawer<Line> {
  private _PolygonConfig: any;
  private _polygonPoints: number[] = [];
  private _mouseupFoleLinePoints: number[] = [];
  private _polygonImagePoints: number[] = [];
  _ratio: any = {
    unit: 'px',
    scales: 1,
  };
  constructor(layer: Layer, viewer: OpenSeadragon.Viewer, polygonConfig: any) {
    super(layer, viewer, polygonConfig);
    this._PolygonConfig = polygonConfig;
  }

  override drawContextmenu(): void {
    const ev = this._PolygonConfig.ev;
    ev.evt.preventDefault();
    const points = this.node?.getAttr('points');
    const maxIndex = points.length - 1 || 0;
    if (
      points.length >= 6 &&
      points[maxIndex - 1] !== points[maxIndex - 3] &&
      points[maxIndex - 2] !== points[maxIndex - 4]
    ) {
      this.status = DrawStatus.drawingCompleted;
      this.node?.setAttr('closed', true);
      this.node?.setAttr('imagePoints', this._polygonImagePoints);
      this._polygonPoints = [];
      this._mouseupFoleLinePoints = [];
      this._polygonImagePoints = [];
      return this.end();
    }
  }

  override drawDown() {
    this.start();
  }

  override drawMove() {
    if (this.node) {
      this.node?.setAttrs({
        points: [...this._polygonPoints, this.curPoint.x, this.curPoint.y],
        imagePoints: [...this._polygonImagePoints, this.curPoint.imageX, this.curPoint.imageY],
        // closed: false
        hitStrokeWidth: 5,
        pixelCoords: {
          x: this.startPoint.imageX,
          y: this.startPoint.imageY,
          imagePoints: [...this._polygonImagePoints, this.curPoint.imageX, this.curPoint.imageY],
        },
      });
    }
  }

  override drawUp() {
    const ev = this._PolygonConfig.ev;
    if (isMouseLeftOrTouch(ev)) {
      // 鼠标左键
      ev.evt.stopPropagation();
      ev.evt.cancelBubble = true;
      this._polygonPoints = [...this._polygonPoints, this.curPoint.x, this.curPoint.y];
      this._mouseupFoleLinePoints = [
        ...this._mouseupFoleLinePoints,
        this.curPoint.x,
        this.curPoint.y,
      ];
      this._polygonImagePoints = [
        ...this._polygonImagePoints,
        this.curPoint.imageX,
        this.curPoint.imageY,
      ];

      if (this._polygonPoints.length === 4) {
        // 双击退出的处理
        const { width, height } = this.getRect(
          {
            x: this._mouseupFoleLinePoints[0],
            y: this._mouseupFoleLinePoints[1],
          },
          this.curPoint
        );
        if (width < this.minWidth || height < this.minHeight) {
          this.nodeDestroy();
          this.node = null;
          this.status = DrawStatus.drawingCompleted;
          this._polygonPoints = [];
          this._mouseupFoleLinePoints = [];
          return;
        }
      }

      // if (this._polygonPoints.length > 4) {
      //   // 判断是否在第一个点附近
      //   const startPoint = this._polygonPoints.slice(0, 2);
      //   const offset = 2;
      //   if (
      //     (this.curPoint.x > startPoint[0] - offset && this.curPoint.x < startPoint[0] + offset) ||
      //     (this.curPoint.y > startPoint[1] - offset && this.curPoint.y < startPoint[1] + offset)
      //   ) {
      //     if (this.node) {
      //       // 确保末尾点等于初始点
      //       this.node?.setAttrs({
      //         points: [...this._polygonPoints.slice(0, -2), ...startPoint],
      //       });
      //     }
      //     this.status = DrawStatus.drawingCompleted;
      //     this._polygonPoints = [];
      //     this._mouseupFoleLinePoints = [];
      //     this.end();
      //   }
      // }

      if (!this.node) {
        this.node = this.createNode();
        this.layer.add(this.node);
        this.setNodeId();
        return this.node;
      } else {
        this.node?.setAttrs({
          points: [...this._polygonPoints],
          imagePoints: [...this._polygonImagePoints],
          pixelCoords: {
            x: this.startPoint.imageX,
            y: this.startPoint.imageY,
            imagePoints: [...this._polygonImagePoints, this.curPoint.imageX, this.curPoint.imageY],
          },
        });
      }
    }
  }

  override drawDblClick() {
    return;
  }

  // 创建rect 抽离方便后续addmarker调用
  createNode() {
    const node = new Line({
      ...this._PolygonConfig.config,
      points: [this.startPoint.x, this.startPoint.y],
      imagePoints: this._polygonImagePoints,
      lineCap: 'round',
      lineJoin: 'round',
      closed: false,
      name: DrawType.polygon,
      strokeScaleEnabled: false,
      zoom: this.viewer.viewport.getZoom(),
      hitStrokeWidth: 30,
    });
    node.on('dragmove dragend transform transformend', (e: any) => {
      const originalPoints = node.getAttrs().points;
      const transform = node.getAbsoluteTransform();
      const resultPoints = [];
      for (let i = 0; i < originalPoints.length; i += 2) {
        const x = originalPoints[i];
        const y = originalPoints[i + 1];
        const pt = transform.point({ x, y });
        const resultPoint = this.viewCoordinatesToImageCoordinates(this.viewer, pt.x, pt.y);
        resultPoints.push(resultPoint.imageX, resultPoint.imageY);
      }
      node?.setAttrs({
        pixelCoords: {
          x: resultPoints[0],
          y: resultPoints[1],
          imagePoints: resultPoints,
        },
      });
    });
    if (this._PolygonConfig.needCountArea) {
      node.setAttr('sceneFunc', (con: Context, shape: any) => {
        con.beginPath();
        con.moveTo(shape.points()[0], shape.points()[1]);
        for (let i = 2; i < shape.points().length; i += 2) {
          con.lineTo(shape.points()[i], shape.points()[i + 1]);
        }
        con.lineTo(shape.points()[0], shape.points()[1]);
        con.closePath();
        con.fillStrokeShape(shape);
        con.fillStyle = shape.attrs.stroke;
        con.scale(
          Math.min(1, 2 / this.layerScale) / shape.scaleX(),
          Math.min(1, 2 / this.layerScale) / shape.scaleY()
        );
        if (this.selectNode?.attrs?.id === node.attrs?.id || this.status !== DrawStatus.end) {
          if(!node.attrs?.pixelCoords?.imagePoints) return
          const area = this.closedAreaPerimeter(
            node.attrs?.pixelCoords?.imagePoints,
            {
              scaleX: 1,
              scaleY: 1,
            },
            this._ratio
          );
          const Description = area;
          con.fillText(
            Description,
            (node.attrs?.pixelCoords?.imagePoints[0] / Math.min(1, 2 / this.layerScale)) * shape.scaleX(),
            (node.attrs?.pixelCoords?.imagePoints[1] / Math.min(1, 2 / this.layerScale)) * shape.scaleY()
          );
          // if(this.status === DrawStatus.select) {
          node?.setAttrs({
            Description,
            scaleRuler: shape.attrs.scaleRuler || 1,
          });
          // }
        } else {
          con.fillText(
            shape.attrs.Description || '',
            (shape.points()[0] / Math.min(1, 2 / this.layerScale)) * shape.scaleX(),
            (shape.points()[1] / Math.min(1, 2 / this.layerScale)) * shape.scaleY()
          );
        }
      });
    }
    return node;
  }

  polygonAreaPerimeter() {}
}
