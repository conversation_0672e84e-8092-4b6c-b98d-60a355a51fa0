import { BaseDrawer } from './BaseDrawer';
import { Line, LineConfig } from 'konva/lib/shapes/Line';
import OpenSeadragon from 'openseadragon';
import { Layer } from 'konva/lib/Layer';
import { DrawType, DrawStatus } from '@/views/Marker/components/editorEnum';
import { isMouseLeftOrTouch } from '@/utils/index';
import { Context } from 'konva/lib/Context';
export class FreeCurveDrawer extends BaseDrawer<Line> {
  private _FreeCurveConfig: any;
  private _freeCurvePoints: number[] = [];
  private _freeCurveImagePoints: number[] = [];
  _ratio: any = {
    unit: 'px',
    scales: 1,
  };
  constructor(layer: Layer, viewer: OpenSeadragon.Viewer, freeCurveConfig: any) {
    super(layer, viewer, freeCurveConfig);
    this._FreeCurveConfig = freeCurveConfig;
  }

  override drawContextmenu(): void {}

  override drawDown() {
    this.start();
    this.startPoint = this.curPoint;
    this._freeCurvePoints = [...this._freeCurvePoints, this.curPoint.x, this.curPoint.y];
    this._freeCurveImagePoints = [
      ...this._freeCurveImagePoints,
      this.curPoint.imageX,
      this.curPoint.imageY,
    ];
    if (!this.node) {
      this.node = this.createNode();
      this.layer.add(this.node);
      this.setNodeId();
      return this.node;
    }
  }

  override drawMove() {
    if (this.node) {
      this._freeCurvePoints = [...this._freeCurvePoints, this.curPoint.x, this.curPoint.y];
      this._freeCurveImagePoints = [
        ...this._freeCurveImagePoints,
        this.curPoint.imageX,
        this.curPoint.imageY,
      ];
      this.node?.setAttrs({
        points: this._freeCurvePoints,
        imagePoints: this._freeCurveImagePoints,
        tension: 1,
        pixelCoords: {
          x: this.startPoint.imageX,
          y: this.startPoint.imageY,
          imagePoints: this._freeCurveImagePoints,
        },
      });
    }
  }

  override drawUp() {
    const ev = this._FreeCurveConfig.ev;
    if (isMouseLeftOrTouch(ev)) {
      const { width, height } = this.getRect(this.startPoint, this.curPoint);
      // 鼠标左键
      if (width < this.minWidth || (height < this.minHeight && this._freeCurvePoints.length < 4)) {
        this.nodeDestroy();
      }
      this.end();
      this._freeCurvePoints = [];
      this._freeCurveImagePoints = [];
      ev.evt.stopPropagation();
      ev.evt.cancelBubble = true;
    }
  }

  // 创建rect 抽离方便后续addmarker调用
  createNode() {
    const node = new Line({
      ...this._FreeCurveConfig.config,
      points: this._freeCurvePoints,
      imagePoints: this._freeCurveImagePoints,
      lineCap: 'round',
      lineJoin: 'round',
      name: DrawType.freeCurve,
      strokeScaleEnabled: false,
      zoom: this.viewer.viewport.getZoom(),
      hitStrokeWidth: 30,
    });
    node.on('dragmove dragend transform transformend', (e: any) => {
      const originalPoints = node.getAttrs().points;
      const transform = node.getAbsoluteTransform();
      const resultPoints = [];
      for (let i = 0; i < originalPoints.length; i += 2) {
        const x = originalPoints[i];
        const y = originalPoints[i + 1];
        const pt = transform.point({ x, y });
        const resultPoint = this.viewCoordinatesToImageCoordinates(this.viewer, pt.x, pt.y);
        resultPoints.push(resultPoint.imageX, resultPoint.imageY);
      }
      node?.setAttrs({
        pixelCoords: {
          x: resultPoints[0],
          y: resultPoints[1],
          imagePoints: resultPoints,
        },
      });
    });
    if (this._FreeCurveConfig.needCountArea) {
      node.setAttr('sceneFunc', (con: Context, shape: any) => {
        con.beginPath();
        for (let i = 2; i < shape.attrs.points.length; i += 2) {
          con.lineTo(shape.attrs.points[i], shape.attrs.points[i + 1]);
        }
        con.fillStrokeShape(shape as any);
        con.fillStyle = shape.attrs.stroke;
        con.scale(
          Math.min(1, 2 / this.layerScale) / shape.scaleX(),
          Math.min(1, 2 / this.layerScale) / shape.scaleY()
        );
        if (this.selectNode?.attrs?.id === node.attrs?.id || this.status !== DrawStatus.end) {
          if(!node.attrs?.pixelCoords?.imagePoints) return
          const Description = this.freeLengthPerimeter(
            node.attrs?.pixelCoords?.imagePoints,
            {
              scaleX: 1,
              scaleY: 1,
            },
            this._ratio
          );
          con.fillText(
            Description,
            (node.attrs?.pixelCoords?.imagePoints[0] / Math.min(1, 2 / this.layerScale)) * shape.scaleX(),
            (node.attrs?.pixelCoords?.imagePoints[1] / Math.min(1, 2 / this.layerScale)) * shape.scaleY()
          );
          // if(this.status === DrawStatus.select) {
          node?.setAttrs({
            Description,
            scaleRuler: shape.attrs.scaleRuler || 1,
          });
          // }
        } else {
          con.fillText(
            shape.attrs.Description || '',
            (shape.points()[0] / Math.min(1, 2 / this.layerScale)) * shape.scaleX(),
            (shape.points()[1] / Math.min(1, 2 / this.layerScale)) * shape.scaleY()
          );
        }
      });
    }
    return node;
  }
}
