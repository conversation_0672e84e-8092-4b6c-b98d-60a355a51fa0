import { BaseDrawer } from './BaseDrawer';
import { Line, LineConfig } from 'konva/lib/shapes/Line';
import OpenSeadragon from 'openseadragon';
import { Layer } from 'konva/lib/Layer';
import { DrawType, DrawStatus } from '@/views/Marker/components/editorEnum';
import { isMouseLeftOrTouch } from '@/utils/index';
import { Context } from 'konva/lib/Context';
import { PerimeterScale } from './Drawer.types';
export class FoldLineDrawer extends BaseDrawer<Line> {
  private _FoldLineConfig: any;
  private _foleLinePoints: number[] = [];
  private _mouseupFoleLinePoints: number[] = [];
  private _foleLineImagePoints: number[] = [];
  _ratio: any = {
    unit: 'px',
    scales: 1,
  };
  constructor(layer: Layer, viewer: OpenSeadragon.Viewer, foldLineConfig: LineConfig) {
    super(layer, viewer, foldLineConfig);
    this._FoldLineConfig = foldLineConfig;
  }

  override drawContextmenu(): void {
    if (this._mouseupFoleLinePoints.length <= 2) {
      this.nodeDestroy();
    } else {
      this.node?.setAttrs({
        points: this._mouseupFoleLinePoints,
        imagePoints: this._foleLineImagePoints,
        pixelCoords: {
          x: this.startPoint.imageX,
          y: this.startPoint.imageY,
          imagePoints: this._foleLineImagePoints,
        },
      });
    }
    this.status = DrawStatus.drawingCompleted;
    this._foleLinePoints = [];
    this._mouseupFoleLinePoints = [];

    this._foleLineImagePoints = [];
    return this.end();
  }

  override drawDown() {
    this.start();
  }

  override drawMove() {
    if (this.node) {
      this.node?.setAttrs({
        points: [...this._foleLinePoints, this.curPoint.x, this.curPoint.y],
        imagePoints: [...this._foleLineImagePoints, this.curPoint.imageX, this.curPoint.imageY],
        pixelCoords: {
          x: this.startPoint.imageX,
          y: this.startPoint.imageY,
          imagePoints: this._foleLineImagePoints,
        },
      });
    }
  }

  override drawUp() {
    const ev = this._FoldLineConfig.ev;
    if (isMouseLeftOrTouch(ev)) {
      // 鼠标左键
      ev.evt.stopPropagation();
      ev.evt.cancelBubble = true;
      this._foleLinePoints = [...this._foleLinePoints, this.curPoint.x, this.curPoint.y];
      this._foleLineImagePoints = [
        ...this._foleLineImagePoints,
        this.curPoint.imageX,
        this.curPoint.imageY,
      ];
      this._mouseupFoleLinePoints = [
        ...this._mouseupFoleLinePoints,
        this.curPoint.x,
        this.curPoint.y,
      ];
      // 处理双击退出
      if (this._mouseupFoleLinePoints.length === 4) {
        const { width, height } = this.getRect(
          {
            x: this._mouseupFoleLinePoints[0],
            y: this._mouseupFoleLinePoints[1],
          },
          this.curPoint
        );
        if (width < this.minWidth || height < this.minHeight) {
          this.nodeDestroy();
          this.status = DrawStatus.drawingCompleted;
          this._foleLinePoints = [];
          this._foleLineImagePoints = [];
          this._mouseupFoleLinePoints = [];
          return;
        }
      }

      if (!this.node) {
        this.node = this.createNode();
        this.layer.add(this.node);
        this.setNodeId();
        return this.node;
      }
    }
  }

  override drawDblClick() {
    return;
  }

  // 创建node 抽离方便后续addmarker调用
  createNode() {
    const node = new Line({
      ...this._FoldLineConfig.config,
      points: this._foleLinePoints,
      imagePoints: this._foleLineImagePoints,
      lineCap: 'round',
      lineJoin: 'round',
      name: DrawType.foldLine,
      strokeScaleEnabled: false,
      zoom: this.viewer.viewport.getZoom(),
      hitStrokeWidth: 30,
    });
    node.on('dragmove dragend transform transformend', (e: any) => {
      const originalPoints = node.getAttrs().points;
      const transform = node.getAbsoluteTransform();
      const resultPoints = [];
      for (let i = 0; i < originalPoints.length; i += 2) {
        const x = originalPoints[i];
        const y = originalPoints[i + 1];
        const pt = transform.point({ x, y });
        const resultPoint = this.viewCoordinatesToImageCoordinates(this.viewer, pt.x, pt.y);
        resultPoints.push(resultPoint.imageX, resultPoint.imageY);
      }
      node?.setAttrs({
        pixelCoords: {
          x: resultPoints[0],
          y: resultPoints[1],
          imagePoints: resultPoints,
        },
      });
    });

    if (this._FoldLineConfig.needCountArea) {
      node.setAttr('sceneFunc', (con: Context, shape: any) => {
        con.beginPath();
        con.moveTo(shape.attrs.points[0], shape.attrs.points[1]);
        for (let i = 2; i < shape.attrs.points.length; i += 2) {
          con.lineTo(shape.attrs.points[i], shape.attrs.points[i + 1]);
        }
        con.fillStrokeShape(shape as any);
        con.fillStyle = shape.attrs.stroke;
        con.scale(
          Math.min(1, 2 / this.layerScale) / shape.scaleX(),
          Math.min(1, 2 / this.layerScale) / shape.scaleY()
        );
        if (this.selectNode?.attrs?.id === node.attrs?.id || this.status !== DrawStatus.end) {
          if(!node.attrs?.pixelCoords?.imagePoints) return
          const Description = this.freeLengthPerimeter(
            node.attrs?.pixelCoords?.imagePoints,
            {
              scaleX: 1,
              scaleY: 1,
            },
            this._ratio
          );
          con.fillText(
            Description,
            (node.attrs?.pixelCoords?.imagePoints[0] / Math.min(1, 2 / this.layerScale)) * shape.scaleX(),
            (node.attrs?.pixelCoords?.imagePoints[1] / Math.min(1, 2 / this.layerScale)) * shape.scaleY()
          );
          // if(this.status === DrawStatus.select) {
          node?.setAttrs({
            Description,
            scaleRuler: shape.attrs.scaleRuler || 1,
          });
          // }
        } else {
          con.fillText(
            shape.attrs.Description,
            (shape.points()[0] / Math.min(1, 2 / this.layerScale)) * shape.scaleX(),
            (shape.points()[1] / Math.min(1, 2 / this.layerScale)) * shape.scaleY()
          );
        }
      });
    }
    return node;
  }
}
