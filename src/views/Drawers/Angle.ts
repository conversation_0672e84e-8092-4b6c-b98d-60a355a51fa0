import { BaseDrawer } from './BaseDrawer';
import { Line, LineConfig } from 'konva/lib/shapes/Line';
import OpenSeadragon, { Point } from 'openseadragon';
import { Layer } from 'konva/lib/Layer';
import Konva from 'konva';
import { DrawType, DrawStatus } from '@/views/Marker/components/editorEnum';
import { isMouseLeftOrTouch } from '@/utils/index';
import { Context } from 'konva/lib/Context';
export class AngleDrawer extends BaseDrawer<Konva.Line> {
  private _angleConfig: any;
  private _anglePoints: number[] = [];
  private _imagePoints: number[] = [];
  constructor(layer: Layer, viewer: OpenSeadragon.Viewer, angleConfig: any) {
    super(layer, viewer, angleConfig);
    this._angleConfig = angleConfig;
  }

  override drawDown() {
    this.start();
    this.startPoint = this.curPoint;
  }

  override drawMove() {
    if (this.node) {
      this.node?.setAttrs({
        points: [...this._anglePoints, this.curPoint.x, this.curPoint.y],
        closed: false,
        // hitStrokeWidth: 5,
        pixelCoords: {
          x: this.startPoint.imageX,
          y: this.startPoint.imageY,
          imagePoints: [...this._imagePoints, this.curPoint.imageX, this.curPoint.imageY],
        },
      });
    }
  }

  override drawUp() {
    const ev = this._angleConfig.ev;
    if (isMouseLeftOrTouch(ev)) {
      // 鼠标左键
      ev.evt.stopPropagation();
      ev.evt.cancelBubble = true;
      this._anglePoints = [...this._anglePoints, this.curPoint.x, this.curPoint.y];
      this._imagePoints = [...this._imagePoints, this.curPoint.imageX, this.curPoint.imageY];

      if (!this.node) {
        this.node = this.createNode();
        this.layer.add(this.node);
        this.setNodeId();
        return this.node;
      } else {
        this.node?.setAttrs({
          points: this._anglePoints,
          pixelCoords: {
            x: this.startPoint.imageX,
            y: this.startPoint.imageY,
            imagePoints: this._imagePoints,
          },
        });
        if (this._anglePoints.length >= 6) {
          this.end();
          this.status = DrawStatus.drawingCompleted;
          // this.node?.setAttr('closed', true);
          this._anglePoints = [];
          this._imagePoints = [];
        }
      }
      console.log(this.node, 'this.node');
    }
  }

  createNode() {
    const node = new Konva.Line({
      ...this._angleConfig.config,
      points: [this.startPoint.x, this.startPoint.y],
      lineCap: 'round',
      lineJoin: 'round',
      strokeScaleEnabled: false,
      hitStrokeWidth: 30,
      zoom: this.viewer.viewport.getZoom(),
      name: DrawType.angle,
      closed: false,
      sceneFunc: (con: Context, shape: any) => {
        const oriPoints = shape.points();
        const transformPoints = shape?.attrs?.transformPoints;
        const points = transformPoints || oriPoints;
        con.beginPath();
        con.moveTo(oriPoints[0], oriPoints[1]);
        for (let i = 0; i < points.length; i += 2) {
          con.lineTo(oriPoints[i], oriPoints[i + 1]);
        }
        // con.closePath();
        con.fillStrokeShape(shape);
        con.fillStyle = shape.attrs.stroke;
        con.scale(
          Math.min(1, 2 / this.layerScale) / shape.scaleX(),
          Math.min(1, 2 / this.layerScale) / shape.scaleY()
        );
        // console.log( node.attrs?.id, 'node.attrs?.id')
        // if (this.selectNode?.attrs?.id === node.attrs?.id || this.status !== DrawStatus.end) {
        const angleMap = this.getAngle(
          {
            x: points[0] - points[2],
            y: points[1] - points[3],
          },
          {
            x: points[4] - points[2],
            y: points[5] - points[3],
          }
        );
        if (points.length >= 6) {
          const Description = angleMap.trueAngle;
          const oriAngle = angleMap.oriAngle;
          const roaResult = this.rotatePoint(
            oriPoints[0],
            oriPoints[1],
            oriPoints[2],
            oriPoints[3],
            oriAngle
          );

          const distanceA = ((points[0] - points[2]) ** 2 + (points[1] - points[3]) ** 2) ** 0.5;
          const distanceB = ((points[4] - points[2]) ** 2 + (points[5] - points[3]) ** 2) ** 0.5;
          const minDistance = Math.min(distanceA, distanceB);
          let font = minDistance * 0.95;
          const x = (roaResult.x + oriPoints[2]) / 2;
          const y = (roaResult.y + oriPoints[3]) / 2;
          if (font > 12) {
            font = 12;
          } else if (font <= 4) {
            font = 4;
          }
          con.font = `${font}px serif`;
          con.fillText(
            Description,
            (x / Math.min(1, 2 / this.layerScale)) * shape.scaleX(),
            (y / Math.min(1, 2 / this.layerScale)) * shape.scaleY()
          );
          this.node?.setAttrs({
            Description,
            scaleRuler: shape.attrs.scaleRuler || 1,
          });
        }
        // } else {
        //   console.log('sceneFunc false');
        //   const angleMap = this.getAngle(
        //     {
        //       x: points[0] - points[2],
        //       y: points[1] - points[3],
        //     },
        //     {
        //       x: points[4] - points[2],
        //       y: points[5] - points[3],
        //     }
        //   );
        //   const oriAngle = angleMap.oriAngle;
        //   const roaResult = this.rotatePoint(
        //     oriPoints[0],
        //     oriPoints[1],
        //     oriPoints[2],
        //     oriPoints[3],
        //     oriAngle
        //   );
        //   const x = (roaResult.x + oriPoints[2]) / 2;
        //   const y = (roaResult.y + oriPoints[3]) / 2;
        //   con.fillText(
        //     shape.attrs.Description || '',
        //     (x / Math.min(1, 2 / this.layerScale)) * shape.scaleX(),
        //     (y / Math.min(1, 2 / this.layerScale)) * shape.scaleY()
        //   );
        // }
      },
    });
    node.on('transform transformend', (e: any) => {
      const originalPoints = node.getAttrs().points;
      const transform = node.getAbsoluteTransform();
      const resultPoints = [];
      const points = [];
      for (let i = 0; i < originalPoints.length; i += 2) {
        const x = originalPoints[i];
        const y = originalPoints[i + 1];
        const pt = transform.point({ x, y });
        points.push(pt.x);
        points.push(pt.y);
        const resultPoint = this.viewCoordinatesToImageCoordinates(this.viewer, pt.x, pt.y);
        resultPoints.push(resultPoint.imageX, resultPoint.imageY);
      }
      node?.setAttrs({
        transformPoints: points,
        // points,
        pixelCoords: {
          x: resultPoints[0],
          y: resultPoints[1],
          imagePoints: resultPoints,
        },
      });
    });
    return node;
  }

  getAngle = ({ x: x1, y: y1 }: any, { x: x2, y: y2 }: any) => {
    const dot = x1 * x2 + y1 * y2;
    const det = x1 * y2 - y1 * x2;
    const angle = (Math.atan2(det, dot) / Math.PI) * 180;
    const result = Math.round(angle + 360) % 360;
    let trueAngle = '';
    if (result > 180 && result <= 270) {
      trueAngle = 180 - (result - 180) + '°';
    } else if (result > 270) {
      trueAngle = 360 - result + '°';
    } else if (result <= 180) {
      trueAngle = result + '°';
    } else {
      trueAngle = '';
    }
    return {
      trueAngle,
      oriAngle: result,
    };
  };

  rotatePoint(x: number, y: number, cx: number, cy: number, angle: number) {
    let offset = 0;
    if (angle > 180 && angle <= 270) {
      const trueAngle = 180 - (angle - 180);
      angle = angle + trueAngle / 2;
      offset = -4;
    } else if (angle > 270) {
      const trueAngle = 360 - angle;
      angle = angle + trueAngle / 2;
      offset = -4;
    } else if (angle <= 180) {
      angle = angle / 2;
      offset = 4;
    } else {
      angle = angle / 2;
      offset = 4;
    }
    // 将角度转换为弧度
    const rad = (-angle * Math.PI) / 180;
    // console.log(angle, 'angleangleangleangle');
    // 将点 (x, y) 旋转角度 angle，中心点 (cx, cy)
    const cos = Math.cos(rad);
    const sin = Math.sin(rad);

    // 旋转后新坐标
    const xNew = cos * (x - cx) + sin * (y - cy) + cx;
    const yNew = -sin * (x - cx) + cos * (y - cy) + cy;
    return {
      x: xNew,
      y: yNew,
      offset,
    };
  }
}
