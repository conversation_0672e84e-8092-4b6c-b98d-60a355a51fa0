import { BaseDrawer } from './BaseDrawer';
import Kon<PERSON> from 'konva';
import { Rect, RectConfig } from 'konva/lib/shapes/Rect';
import OpenSeadragon, { Point } from 'openseadragon';
import { Layer } from 'konva/lib/Layer';
import { DrawType, DrawStatus } from '@/views/Marker/components/editorEnum';
import { isMouseLeftOrTouch } from '@/utils/index';
import { Context } from 'konva/lib/Context';
export class RectDrawer extends BaseDrawer<Konva.Rect> {
  private _rectConfig: any;

  _ratio: any = {
    unit: 'px',
    scales: 1,
  };

  constructor(layer: Layer, viewer: OpenSeadragon.Viewer, rectConfig: any) {
    super(layer, viewer, rectConfig);
    this._rectConfig = rectConfig;
    this.startPoint = {};
  }

  override drawDown() {
    if (!this.node) {
      this.start();
      this.startPoint = this.curPoint;
      this.node = this.createNode();
      this.layer.add(this.node);
      this.setNodeId();
    }
  }

  override drawMove() {
    const { x, y, width, height } = this.getRect(this.startPoint, this.curPoint);
    const curPoint = this.getCurPoint();
    // const imageX = Math.abs(this.curPoint.imageX - this.startPoint.imageX);
    // const imageY = Math.abs(this.curPoint.imageY - this.startPoint.imageY);
    const leftTopPoint = this.viewCoordinatesToImageCoordinates(this.viewer, x, y);
    this.node?.setAttrs({
      x,
      y,
      width,
      height,
      pixelCoords: {
        x: leftTopPoint.imageX,
        y: leftTopPoint.imageY,
        imagePoints: [
          this.startPoint.imageX,
          this.startPoint.imageY,
          curPoint.imageX,
          curPoint.imageY,
        ],
      },
    });
  }

  override drawUp() {
    if (isMouseLeftOrTouch(this._rectConfig.ev)) {
      const { width, height } = this.getRect(this.startPoint, this.curPoint);
      // 鼠标左键
      if (width < this.minWidth || height < this.minHeight) {
        this.nodeDestroy();
      }
      this.status = DrawStatus.drawingCompleted;
      this.end();
    }
  }

  // override drawDblClick() {
  //   const { width, height } = this.getRect(this.startPoint, this.curPoint);
  //   // 鼠标左键
  //   if (width < this.minWidth || height < this.minHeight) {
  //     this.nodeDestroy();
  //   }
  // }

  // 创建rect 抽离方便后续addmarker调用
  createNode() {
    const { x, y, width, height } = this.getRect(this.startPoint, this.curPoint);
    const node = new Konva.Rect({
      ...this._rectConfig.config,
      x,
      y,
      width,
      height,
      imagePoints: [this.startPoint.imageX, this.startPoint.imageY],
      readingImagePoints: [this.startPoint.imageX, this.startPoint.imageY],
      name: DrawType.rect,
      strokeScaleEnabled: false,
      zoom: this.viewer.viewport.getZoom(),
    });
    node.on('dragmove dragend transform transformend', (e: any) => {
      const { x, y, width, height } = node.getClientRect({
        skipStroke: true,
        skipShadow: true,
      });
      const startPoint = this.viewCoordinatesToImageCoordinates(this.viewer, x, y);
      const curPoint = this.viewCoordinatesToImageCoordinates(this.viewer, x + width, y + height);
      node.setAttrs({
        pixelCoords: {
          x: startPoint.imageX,
          y: startPoint.imageY,
          imagePoints: [startPoint.imageX, startPoint.imageY, curPoint.imageX, curPoint.imageY],
        },
      });
    });
    if (this._rectConfig.needCountArea) {
      node.setAttr('sceneFunc', (con: Context, shape: any) => {
        con.beginPath();
        con.lineWidth = 1 / this.layerScale;
        con.strokeStyle = shape.attrs.stroke;
        con.rect(0, 0, shape.width(), shape.height());
        con.closePath();
        con.fillStrokeShape(shape as any);
        con.fillStyle = shape.attrs.stroke;
        con.scale(
          Math.min(1, 2 / this.layerScale) / shape.scaleX(),
          Math.min(1, 2 / this.layerScale) / shape.scaleY()
        );
        // 只需要创建的时候 select的时候需要重算面积
        if (this.selectNode?.attrs?.id === node.attrs?.id || this.status !== DrawStatus.end) {
          const points = node.getAttr('pixelCoords')?.imagePoints;
          if (!points) return;
          const start = points.slice(0, 2);
          const end = points.slice(2, 4);
          const Description = this._rectAreaPerimeter(
            Math.abs(start?.[0] - end?.[0]),
            Math.abs(start?.[1] - end?.[1])
          );
          con.fillText(Description, 0, -shape.attrs.strokeWidth);
          node?.setAttrs({
            Description,
            imageW: Math.abs(start?.[0] - end?.[0]),
            imageH: Math.abs(start?.[1] - end?.[1]),
          });
        } else {
          con.fillText(shape.attrs.Description || '', 0, -shape.attrs.strokeWidth);
        }
      });
    }
    return node;
  }

  /**
   * 计算矩形面积宽高
   */
  private _rectAreaPerimeter: (width: number, height: number) => string = (width, height) => {
    const w = width * this._ratio.scales;
    const h = height * this._ratio.scales;
    return (
      (w * h).toFixed(2) + this._ratio.unit + '² ' + (2 * w + 2 * h).toFixed(2) + this._ratio.unit
    );
  };
}
