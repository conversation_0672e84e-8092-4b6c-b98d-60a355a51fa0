import { BaseDrawer } from './BaseDrawer';
import Konva from 'konva';
import { Line, LineConfig } from 'konva/lib/shapes/Line';
import OpenSeadragon, { Point } from 'openseadragon';
import { Layer } from 'konva/lib/Layer';
import { DrawType, DrawStatus } from '@/views/Marker/components/editorEnum';
import { isMouseLeftOrTouch } from '@/utils/index';
import { Context } from 'konva/lib/Context';
export class RulerDrawer extends BaseDrawer<Konva.Line | Konva.Group> {
  private _lineConfig: any;
  private _line: Konva.Line;
  private _startCircle: Konva.Circle;
  private _endCircle: Konva.Circle;
  constructor(layer: Layer, viewer: OpenSeadragon.Viewer, lineConfig: any) {
    super(layer, viewer, lineConfig);
    this._lineConfig = lineConfig;
  }

  get needResize() {
    return this._lineConfig.needResize;
  }

  _ratio: any = {
    unit: 'px',
    scales: 1,
  };
  override drawDown() {
    if (!this.node) {
      this.start();
      this.startPoint = this.curPoint;
      this.node = this.createNode();
      this.layer.add(this.node);
      this.setNodeId();
    }
  }

  override drawMove() {
    this._line?.setAttrs({
      points: [this.startPoint.x, this.startPoint.y, this.curPoint.x, this.curPoint.y],
      imagePoints: [
        this.startPoint.imageX,
        this.startPoint.imageY,
        this.curPoint.imageX,
        this.curPoint.imageY,
      ],
      pixelCoords: {
        x: this.startPoint.imageX,
        y: this.startPoint.imageY,
        imagePoints: [this.startPoint.x, this.startPoint.y, this.curPoint.x, this.curPoint.y],
      },
    });
  }

  override drawUp() {
    if (isMouseLeftOrTouch(this._lineConfig.ev)) {
      const { width, height } = this.getRect(this.startPoint, this.curPoint);
      // 鼠标左键
      if (width < this.minWidth || height < this.minHeight) {
        this.nodeDestroy();
      }
      this._endCircle.setAttrs({
        x: this.curPoint.x,
        y: this.curPoint.y,
        radius: 1,
      });
      this.status = DrawStatus.drawingCompleted;
      this.end();
      this._line.setAttr('dash', []);
    }
  }

  // 创建rect 抽离方便后续addmarker调用
  createNode() {
    const node = new Konva.Group({
      tName: 'Group_Ruler',
      name: DrawType.ruler,
    });
    this._line = new Konva.Line({
      ...this._lineConfig.config,
      points: [this.startPoint.x, this.startPoint.y],
      imagePoints: [
        this.startPoint.imageX,
        this.startPoint.imageY,
        this.curPoint.imageX,
        this.curPoint.imageX,
      ],
      hitStrokeWidth: 30,
      tName: 'Group_Ruler_Line_Base',
      strokeScaleEnabled: false,
      zoom: this.viewer.viewport.getZoom(),
      sceneFunc: (con: Context, shape: any) => {
        con.beginPath();
        for (let i = 0; i < shape.points().length; i += 2) {
          con.lineTo(shape.points()[i], shape.points()[i + 1]);
        }
        con.closePath();
        con.fillStrokeShape(shape as any);
        con.fillStyle = shape.attrs.stroke;
        con.scale(
          Math.min(1, 2 / this.layerScale) / shape.scaleX(),
          Math.min(1, 2 / this.layerScale) / shape.scaleY()
        );
        if (this.selectNode?.attrs?.id === node.attrs?.id || this.status !== DrawStatus.end) {
          let endPoint = {
            imageX: shape.attrs.imagePoints[2],
            imageY: shape.attrs.imagePoints[3],
          };
          let startpoint = {
            imageX: shape.attrs.imagePoints[0],
            imageY: shape.attrs.imagePoints[1],
          };
          const textPosition = {
            x:
              (((shape.attrs.points[2] - shape.attrs.points[0]) /
                Math.min(1, 2 / this.layerScale)) *
                shape.scaleX()) /
              2,
            y:
              (((shape.attrs.points[3] - shape.attrs.points[1]) /
                Math.min(1, 2 / this.layerScale)) *
                shape.scaleY()) /
              2,
          };
          const Description =
            this.pointPerimeter(
              {
                imageX: startpoint.imageX * (shape.attrs.scaleX || 1),
                imageY: startpoint.imageY * (shape.attrs.scaleY || 1),
              },
              {
                imageX: endPoint.imageX * (shape.attrs.scaleX || 1),
                imageY: endPoint.imageY * (shape.attrs.scaleY || 1),
              },
              this._ratio
            ) + this._ratio.unit;
          con.translate(
            (shape.points()[0] / Math.min(1, 2 / this.layerScale)) * shape.scaleX(),
            (shape.points()[1] / Math.min(1, 2 / this.layerScale)) * shape.scaleY()
          );
          con.fillText(Description || '', textPosition.x, textPosition.y - 5);
          this._line?.setAttrs({
            Description,
            imageW: Math.abs(this.startPoint.imageX - endPoint.imageX),
            imageH: Math.abs(this.startPoint.imageY - endPoint.imageY),
          });
        } else {
          const textPosition = {
            x:
              (((shape.attrs.points[2] - shape.attrs.points[0]) /
                Math.min(1, 2 / this.layerScale)) *
                shape.scaleX()) /
              2,
            y:
              (((shape.attrs.points[3] - shape.attrs.points[1]) /
                Math.min(1, 2 / this.layerScale)) *
                shape.scaleY()) /
              2,
          };
          con.translate(
            (shape.points()[0] / Math.min(1, 2 / this.layerScale)) * shape.scaleX(),
            (shape.points()[1] / Math.min(1, 2 / this.layerScale)) * shape.scaleY()
          );
          con.fillText(shape.attrs.Description || '', textPosition.x, textPosition.y - 5);
        }
      },
    });
    this._startCircle = new Konva.Circle({
      x: this.startPoint.x,
      y: this.startPoint.y,
      radius: 1,
      fill: 'red',
      draggable: true,
      tName: 'Group_Ruler_StartCircle_Anchor',
      visible: false,
    });
    this._endCircle = new Konva.Circle({
      //   x: this.startPoint.x,
      //   y: this.startPoint.y,
      //   radius: 1,
      fill: 'blue',
      draggable: true,
      tName: 'Group_Ruler_EndCircle_Anchor',
      visible: false,
    });
    this._startCircle.on('dragmove', (event) => {
      const { x, y, width, height } = this._endCircle.getClientRect();
      const curPoint = this._konvaPointFromPixl(x + width / 2, y + height / 2);
      this._startCircle.setAttrs({
        imageX: curPoint.imageX,
        imageY: curPoint.imageY,
      });

      this._line.points([
        this._startCircle.x(),
        this._startCircle.y(),
        this._endCircle.x(),
        this._endCircle.y(),
      ]);

      const startX = this._startCircle.attrs.imageX;
      const startY = this._startCircle.attrs.imageY;
      const { imagePoints } = this._line.attrs;
      this._line.setAttrs({
        imagePoints: [startX, startY, imagePoints[2], imagePoints[3]],
        pixelCoords: {
          imagePoints: [startX, startY, imagePoints[2], imagePoints[3]],
        },
      });
    });
    this._endCircle.on('dragmove', (event) => {
      const { x, y, width, height } = this._endCircle.getClientRect();
      const curPoint = this._konvaPointFromPixl(x + width / 2, y + height / 2);
      this._endCircle.setAttrs({
        imageX: curPoint.imageX,
        imageY: curPoint.imageY,
      });
      this._line.points([
        this._startCircle.x(),
        this._startCircle.y(),
        this._endCircle.x(),
        this._endCircle.y(),
      ]);

      const endX = this._endCircle.attrs.imageX;
      const endY = this._endCircle.attrs.imageY;
      const { imagePoints } = this._line.attrs;
      this._line.setAttrs({
        imagePoints: [imagePoints[0], imagePoints[1], endX, endY],
        pixelCoords: {
          imagePoints: [imagePoints[0], imagePoints[1], endX, endY],
        },
      });
    });
    node.on('dragmove', (e: any) => {
      const { x: startX, y: startY } = this._startCircle.absolutePosition();
      const { x: endX, y: endY } = this._endCircle.absolutePosition();
      const startPoint = this._konvaPointFromPixl(startX, startY);
      const endPoint = this._konvaPointFromPixl(endX, endY);
      this._line.setAttrs({
        imagePoints: [startPoint.imageX, startPoint.imageY, endPoint.imageX, endPoint.imageY],
        pixelCoords: {
          imagePoints: [startPoint.imageX, startPoint.imageY, endPoint.imageX, endPoint.imageY],
        },
      });
    });
    node.add(this._line);
    node.add(this._startCircle);
    node.add(this._endCircle);
    return node;
  }
}
