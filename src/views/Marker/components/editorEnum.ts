//画图状态
// 需要几种状态？     end select
export enum DrawStatus {
  drawing = 'drawing', //正在画图
  drawingCompleted = 'drawingCompleted', // 画图完成
  end = 'end', //结束画图
  select = 'select', //选择
  moving = 'moving', // 移动中
  anchorStart = 'anchorStart',
}

//绘画形状
export enum DrawType {
  line = 'line',
  arrow = 'arrow',
  rect = 'rect',
  circle = 'circle',
  ellipse = 'ellipse', // 椭圆
  angle = 'angle', // 夹角
  ring = 'ring', // 同心圆
  flag = 'flag', // 标旗
  foldLine = 'foldLine', // 折线
  polygon = 'polygon', // 多边形
  closedCurve = 'closedCurve', // 闭合曲线
  freeCurve = 'freeCurve', // 自由曲线
  ruler = 'ruler', // 尺子
  text = 'text',
  wand = 'wand', // 魔杖
}

// 绘画形状对应的中文名
export enum DrawCname {
  line = '直线',
  arrow = '箭头',
  rect = '矩形',
  circle = '圆形',
  ellipse = '椭圆',
  angle = '夹角',
  ring = '同心圆',
  flag = '标旗',
  foldLine = '折线',
  polygon = '多边形',
  closedCurve = '闭合曲线',
  freeCurve = '自由曲线',
  ruler = '刻度尺',
  wand = '魔杖',
}

//工具类型
export enum BarType {
  rect = 'rect',
  line = 'line',
  ellipse = 'ellipse',
  arrow = 'arrow',
  text = 'text',
}

export enum HistoryRecordTypes {
  create = 'create',
  edit = 'edit',
  delete = 'delete',
  // CRUD 创建查看修改删除
}

export interface DrawPoint {
  x: number;
  y: number;
  imageX?: number;
  imageY?: number;
  pcx?: number;
  pcy?: number;
}
