<template>
  <div style="position: absolute; top: 150px; left: 250px; z-index: 999">
    <button @click="showSomeAreas">AI区域切换</button>
    <button @click="handlePosition">定位</button>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from 'vue';
import PixiAIMarker from './index';
import OpenSeadragon from 'openseadragon';

const props = defineProps({
  viewer: OpenSeadragon.Viewer,
  flip: Boolean,
});

// const fetchAIUrl = async () => {
//   axios({
//     url: `http://**************:7015/api/slice-inner-ai-label/${encodeURIComponent(
//       `B202210036-004-ihc`
//     )}/Ai-result`,
//     method: 'get',
//   }).then((res: any) => {
//     console.log(res.data.data, 'res1');
//     aiUrl.value = res.data.data;
//     fetchAI();
//   });
// };

// const fetchAI = () => {
// axios({
//   url: aiUrl.value,
//   method: 'get',
//   // params: { sliceName },
// }).then((res: any) => {
//   aiResult.value = res.data;
//   pixi.value?.addMarkerByGroupModel(res.data.GroupModel)
// });
// };

const handlePosition = () => {
  console.log(pixi.value, 'pixi.value');
  pixi.value?.pixiOverlay.positioningOSDByPixiPoint(35072 / 150, 101632 / 150);
  pixi.value?.pixiOverlay.showHKInfo({
    x: 35072 / 150,
    y: 101632 / 150,
    rectWidth: 100,
    rectHeight: 100,
    stringArray: ['asc_us', '111211231232131231um²'],
  });
};
const fetchJsonData = () => {
  // fetch('./jsonData/mianzizuhua/B202210036-004ER.json')
  // fetch('./jsonData/mianzizuhua/S111.json')
  // fetch('./jsonData/mianzizuhua/*********-P16.json')
  // fetch('./jsonData/mianzizuhua/B202215036-002HER-2.json')
  // fetch('./jsonData/lymphnode/2024101501.json');
  // fetch('./jsonData/stomach/D201900138-001.json')
  // fetch('./jsonData/er/B202210036-004ER.json')
  // fetch('./jsonData/KI67/Q22.json')
  // fetch('./jsonData/KI67/B202210469-027ki-67.json')
  // fetch('./jsonData/intestine/Q24-05766.json') 
  // fetch('./jsonData/tumor/tumor2_tumor_regions.json') 
  // fetch('./jsonData/gongjin/**********-1.json') 
  fetch('./jsonData/other/hk1.json') 
    .then((response) => {
      console.log(response, 'response');
      return response.json();
    })
    .then((data) => {
      console.log(data, 'datadatadata');
      // pixi.value?.pixiMarker.addMarkersByGroupModel(data.GroupModel);
      // pixi.value?.pixiMarker.addLymMarkersByGroupModel(data.GroupModel);
      // pixi.value?.pixiMarker.addStomachMarkersByGroupModel(data.GroupModel);
      // pixi.value?.pixiMarker.addIntestineMarkersByGroupModel(data.GroupModel);
      console.log(pixi.value?.pixiMarker.addHKMarkersByGroupModel(data.GroupModel), 'labels')
    });
};

const pixi = ref<PixiAIMarker | null>(null);
onMounted(async () => {
  if (props.viewer) {
    pixi.value = new PixiAIMarker(props.viewer, {
      showAll: true,
      pixiIsV8: false,
    });
  }
  // await fetchAIUrl();
  setTimeout(() => {
    fetchJsonData();
  }, 2000);
});
const showSomeAreas = () => {
  pixi.value?.pixiMarker.showAreas(['ROI']);
  // pixi.value?.pixiMarker.showAreasByIds([1]);
};
watch(
  () => props.flip,
  (value) => {
    // if (value) {
    pixi.value?.pixiMarker.setFlip(value);
    // }
  }
);
</script>

<style lang="scss"></style>
