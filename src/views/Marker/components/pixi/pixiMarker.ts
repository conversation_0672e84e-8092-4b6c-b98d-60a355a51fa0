import OpenSeadragon, { MouseTracker } from 'openseadragon';
import {
  Marker,
  Group,
  CancerType,
  ContainerType,
  PixiAIMarkerOptions,
  TYPES,
  AreasMap,
} from './pixi.types';
import { Application, Container, Graphics, Rectangle } from 'pixi.js';
import * as PIXI from 'pixijs';

export default class PixiAIMarker {
  _container: ContainerType;
  _drawDoneCallback: Function;
  _options: PixiAIMarkerOptions;
  _viewer: OpenSeadragon.Viewer;

  DCIS_Area: Marker[] = [];
  DCIS_Positive_Area: Marker[] = [];
  DCIS_Negative_Area: Marker[] = [];
  Invasive_Area: Marker[] = [];
  Invasive_Positive_Area: Marker[] = [];
  Invasive_Negative_Area: Marker[] = [];
  ROI: Marker[] = [];
  areasMap: any = {};
  labelsMap: any = {}; // 所有labels集合

  isFlip: boolean = false;

  constructor(viewer: OpenSeadragon.Viewer, container: ContainerType, options: any) {
    this._viewer = viewer;
    this._container = container;
    this._options = options;
  }

  drawDoneCallback(cb: Function) {
    this._drawDoneCallback = cb;
  }

  flatGroupModal(GroupModel: Group): void {
    if (GroupModel.Groups?.length > 0) {
      for (let i = 0; i < GroupModel.Groups.length; i++) {
        const group = GroupModel.Groups[i];
        this.labelsMap[group.Name] = group.Labels;
        this.flatGroupModal(group);
      }
    }
  }
  addMarkersByGroupModel(GroupModel: Group | null) {
    this.flatGroupModal(GroupModel as Group);
    // console.log(this.labelsMap, 'labelsMap');
    for (const key in this.labelsMap) {
      const item = this.labelsMap[key];
      this.addGraphicsByLabels(this._container, item, key);
    }
    // console.log(this.areasMap, 'areasmap');
    this.getMarkerTotal();
    this._drawDoneCallback?.();
    if (!this._options?.showAll) {
      this.showAreas([]);
    }
  }

  // 淋巴结的预览
  addLymMarkersByGroupModel(GroupModel: Group) {
    // 淋巴结 --- ID65792 -- 转移非转移的区域都在一个labels里。
    // 根据区域颜色FillBackgroundColor 蓝红来区分 红为转移
    const blue: Marker[] = [];
    const red: Marker[] = [];
    const result: Marker[] = []; // 转移
    GroupModel.Labels.forEach((item: any) => {
      if (
        item.Name.indexOf('result_') !== -1 &&
        item.FillBackgroundColor === '#FF8C00' &&
        item.Type === 'btn_brush'
      ) {
        result.push(item);
        this.addGraphicsByLabels(this._container, [item], `Lym_Result_Area_${item.ID}`);
      } else if (item.LineColor === '#0000FF' && item.Type === 'btn_brush') {
        blue.push(item);
        this.addGraphicsByLabels(this._container, [item], `Lym_Blue_Area_${item.ID}`);
      } else if (item.LineColor === '#FF0000' && item.Type === 'btn_brush') {
        red.push(item);
        this.addGraphicsByLabels(this._container, [item], `Lym_Red_Area_${item.ID}`);
      }
    });
    this.addGraphicsByLabels(this._container, blue, 'Lym_Blue_Area');
    this.addGraphicsByLabels(this._container, red, 'Lym_Red_Area');
    this.addGraphicsByLabels(this._container, result, 'Lym_Result_Area');
    const lymNum = blue?.length + red?.length;
    const redLymNum = red?.length;
    const blueImgs = this.calcImgProps(blue);
    const redImgs = this.calcImgProps(red);
    const resultImgs = this.calcImgProps(result);
    // console.log(this.areasMap, 'areaMap');
    if (!this._options?.showAll) {
      this.showAreas([]);
    }
    return {
      lymNum,
      redLymNum,
      blueImgs,
      redImgs,
      resultImgs,
    };
  }

  // 淋巴结计算左上角坐标 以及 宽度 高度  来获取截图
  calcImgProps(list: Marker[]) {
    const result = list.map((item) => {
      const coords = item.Coordinates;
      if (coords) {
        const attrs = coords.reduce(
          (acc: any, cur: any) => {
            const curX = cur?.X;
            const curY = cur?.Y;
            if (!acc.minX || curX < acc.minX) {
              acc.minX = curX;
            }
            if (!acc.maxX || curX > acc.maxX) {
              acc.maxX = curX;
            }
            if (!acc.minY || curY < acc.minY) {
              acc.minY = curY;
            }
            if (!acc.maxY || curY > acc.maxY) {
              acc.maxY = curY;
            }
            return acc;
          },
          {
            minX: undefined,
            maxX: undefined,
            minY: undefined,
            maxY: undefined,
          }
        );
        return {
          id: item.ID,
          pt: {
            x: attrs.minX,
            y: attrs.minY,
          },
          width: attrs.maxX - attrs.minX,
          height: attrs.maxY - attrs.minY,
          area: item.Area,
        };
      }
    });
    return result;
  }

  // 胃的预览
  addStomachMarkersByGroupModel(GroupModel: Group) {
    try {
      const labels = GroupModel.Labels;
      this.addGraphicsByLabels(this._container, labels, 'Stomach_Area');
      return GroupModel.AdditionalInformation;
    } catch (error) {
      console.error(error);
    }
  }

  // 肠的预览
  addIntestineMarkersByGroupModel(GroupModel: Group) {
    try {
      const labels = GroupModel.Labels;
      this.addGraphicsByLabels(this._container, labels, 'Intestine_Area');
      return GroupModel.AdditionalInformation;
    } catch (error) {
      console.error(error);
    }
  }

  // 前列腺的预览
  addProstateMarkersByGroupModel(GroupModel: Group) {
    try {
      const labels = GroupModel.Labels;
      this.addGraphicsByLabels(this._container, labels, 'Prostate_Area');
      return GroupModel.AdditionalInformation;
    } catch (error) {
      console.error(error);
    }
  }

  // 宫颈的预览
  addCervixMarkersByGroupModel(GroupModel: Group) {
    try {
      const labels = GroupModel.Labels;
      this.addGraphicsByLabels(this._container, labels, 'Cervix_Area');
      return GroupModel.AdditionalInformation;
    } catch (error) {
      console.error(error);
    }
  }

  // HK的AI预览
  addHKMarkersByGroupModel(GroupModel: Group) {
    try {
      const labels = GroupModel.Labels;
      const m = new Map();
      for (let index = 0; index < labels.length; index++) {
        const element = labels[index];
        const name = element.Name || 'other';
        if (m.has(name)) {
          const array = m.get(name);
          array.push(element);
          m.set(name, array);
        } else {
          m.set(name, [element]);
        }
      }
      // const labels = GroupModel.Labels;
      // this.addGraphicsByLabels(this._container, labels, 'Cervix_Area');
      // return GroupModel.AdditionalInformation;
      m.forEach((value, key) => {
        this.addGraphicsByLabels(this._container, value, key);
      });
      return {
        map: m,
        info: GroupModel.AdditionalInformation,
      };
    } catch (error) {
      console.error(error);
    }
  }

  addGraphicsByLabels(container: ContainerType, labels: Marker[], key: string) {
    const isV8 = this._options.pixiIsV8;
    const graphics = isV8 ? new Graphics() : new PIXI.Graphics();
    this.areasMap[key] = graphics;
    graphics.visible = true;
    graphics.interactive = false;
    graphics.hitArea = null;
    const imageItem = this._viewer.world.getItemAt(0);
    const ratio = process.env.NODE_ENV === 'development' ? 150 : 1000;
    for (let index = 0; index < labels.length; index++) {
      const item = labels[index];

      if (!item.Coordinates[0]?.X) {
        let coordinates: { X: number; Y: number }[] = [];
        (item.Coordinates as unknown as Array<string>).forEach((value: any) => {
          if (typeof value.X === 'number' && typeof value.Y === 'number') {
            coordinates.push({ X: value.X, Y: value.X });
          } else {
            let data = value.split(',');
            coordinates.push({ X: Number(data[0]), Y: Number(data[1]) });
          }
        });
        item.Coordinates = coordinates;
      }

      const posList: any[] = [];
      item.Coordinates.forEach((it, value) => {
        let ps = null;
        if (imageItem) {
          const pt = new OpenSeadragon.Point(Number(it.X), Number(it.Y));
          ps = imageItem.imageToViewportCoordinates(pt.x, pt.y, true);
        } else {
          // 之前用的是这个方法 线上环境存在问题
          ps = this._viewer.viewport.imageToViewportCoordinates(
            new OpenSeadragon.Point(Number(it.X), Number(it.Y))
          );
        }
        posList.push({
          x: ps.x * ratio,
          y: ps.y * ratio,
        });
      });

      if (isV8) {
        const color = item.LineColor.slice(3);
        const bgColor = item.FillBackgroundColor.slice(3);
        const g = graphics as Graphics;
        if (item.Type === TYPES.btn_rect) {
          (graphics as Graphics).rect(
            posList[0].x,
            posList[0].y,
            posList[1].x - posList[0].x,
            posList[1].y - posList[0].y
          );
          if (item.LineColor) {
            g.stroke({
              color: color,
              width: item.LineWidth,
              alpha: item.Opacity,
            });
          } else {
            g.stroke({
              width: 0,
            }).fill({
              alpha: item.Opacity,
              color: bgColor,
            });
          }
        } else if (item.Type === TYPES.btn_elips) {
          g.ellipse(
            (posList[1].x + posList[0].x) / 2,
            (posList[1].y + posList[0].y) / 2,
            (posList[1].x - posList[0].x) / 2,
            (posList[1].y - posList[0].y) / 2
          );
          if (item.LineColor) {
            g.stroke({
              color: color,
              width: item.LineWidth,
            });
          } else {
            g.stroke({
              width: 0,
            }).fill({
              alpha: item.Opacity,
              color: bgColor,
            });
          }
        } else if (item.Type === TYPES.btn_brush) {
          g.poly(posList);
          if (item.LineColor) {
            g.stroke({
              color: color,
              width: item.LineWidth,
            });
          } else {
            g.stroke({
              width: 0,
            }).fill({
              alpha: item.Opacity,
              color: bgColor,
            });
          }
        } else if (item.Type === TYPES.btn_pen) {
          g.poly(posList);
          if (item.LineColor) {
            g.stroke({
              color: color,
              width: item.LineWidth,
            });
          } else {
            g.stroke({
              width: 0,
            }).fill({
              alpha: item.Opacity,
              color: bgColor,
            });
          }
        } else if (item.Type === TYPES.btn_point) {
          posList.forEach((pos) => {
            if (item.LineColor) {
              g.stroke({
                color: color,
                // width: item.LineWidth,
              });
            } else {
              g.stroke(0);
            }
            g.fill({
              alpha: item.Opacity,
              color: bgColor,
            });
            g.circle(pos.x, pos.y, 0.2);
          });
        }
      } else {
        const g = graphics as PIXI.Graphics;
        const color = this.hexToShortHex(item.LineColor);
        const bgColor = this.hexToShortHex(item.FillBackgroundColor);
        console.log(color, bgColor, 'bg');
        if (item.Type === TYPES.btn_rect) {
          if (item.LineColor) {
            g.lineStyle({
              color: color,
              width: item.LineWidth,
              native: true,
            });
          } else {
            g.lineStyle(0);
          }
          bgColor && g.beginFill(bgColor, item.Opacity);
          // g.drawPolygon(posList);
          g.drawRect(
            posList[0].x,
            posList[0].y,
            posList[1].x - posList[0].x,
            posList[1].y - posList[0].y
          );
        } else if (item.Type === TYPES.btn_elips) {
          if (item.LineColor) {
            g.lineStyle({
              color: color,
              width: item.LineWidth,
              native: true,
            });
          } else {
            g.lineStyle(0);
          }
          g.beginFill(bgColor, 0.5);
          g.drawEllipse(
            (posList[1].x + posList[0].x) / 2,
            (posList[1].y + posList[0].y) / 2,
            (posList[1].x - posList[0].x) / 2,
            (posList[1].y - posList[0].y) / 2
          );
        } else if (item.Type === TYPES.btn_brush) {
          if (item.LineColor) {
            g.lineStyle({
              color: color || bgColor,
              width: item.LineWidth,
              native: true,
            });
          } else {
            g.lineStyle(0);
          }
          g.beginFill(bgColor, item.Opacity);
          g.drawPolygon(posList);
        } else if (item.Type === TYPES.btn_pen) {
          if (item.LineColor) {
            g.lineStyle({
              color: color || bgColor,
              width: item.LineWidth,
              native: true,
            });
          } else {
            g.lineStyle(0);
          }
          g.beginFill(bgColor, item.Opacity);
          g.drawPolygon(posList);
        } else if (item.Type === TYPES.btn_point) {
          posList.forEach((pos) => {
            if (item.LineColor) {
              g.lineStyle({
                color: color,
                // width: item.LineWidth,
                native: true,
              });
            } else {
              g.lineStyle(0);
            }
            g.beginFill(bgColor, item.Opacity);
            g.drawCircle(pos.x, pos.y, 0.1);
          });
        }
        g.endFill();
      }
    }
    if (isV8) {
      (container as Container).addChild(graphics as Graphics);
    } else {
      (container as PIXI.Container).addChild(graphics as PIXI.Graphics);
    }
  }

  handleLabelsItem() {}

  hexToShortHex(hexString: string): number | undefined {
    // #FF0000FF #0000FF
    if (!hexString) {
      return undefined;
    }

    if (hexString.length !== 9 && hexString.length >= 0) {
      hexString = `#FF${hexString.slice(1)}`;
    }
    const red = parseInt(hexString.slice(3, 5), 16);
    const green = parseInt(hexString.slice(5, 7), 16);
    const blue = parseInt(hexString.slice(7, 9), 16);

    const hexColor = (red << 16) | (green << 8) | blue;
    return hexColor;
  }

  getMarkerTotal() {
    let result = 0;
    for (const key in this.labelsMap) {
      const item = this.labelsMap[key];
      result += item.length;
    }
    console.log(result, 'length');
    return result;
  }

  // 需要展示的区域
  showAreas(areasList: (keyof AreasMap)[]) {
    if (Array.isArray(areasList)) {
      // console.log(this.areasMap, 'this.areasMap');
      for (const key in this.areasMap) {
        if (Object.prototype.hasOwnProperty.call(this.areasMap, key)) {
          const graphics = this.areasMap[key as keyof AreasMap];
          if (graphics) {
            graphics.visible = false;
          }
        }
      }
      areasList.forEach((area) => {
        if (this.areasMap[area]) {
          this.areasMap[area].visible = true;
        }
      });
    }
  }
  // 需要展示的区域
  showAreasByIds(idList: number[] | string[]) {
    if (Array.isArray(idList)) {
      for (const key in this.areasMap) {
        if (Object.prototype.hasOwnProperty.call(this.areasMap, key)) {
          const graphics = this.areasMap[key as keyof AreasMap];
          if (graphics) {
            graphics.visible = false;
          }
          // A_B_C_ID  筛出ID
          const l = key.split('_');
          const id =
            Number(l[l.length - 1]) === Number(l[l.length - 1]) ? Number(l[l.length - 1]) : -1;
          const index = idList.findIndex((item) => item === id);
          if (index !== -1) {
            this.areasMap[key].visible = true;
          }
        }
      }
    }
  }

  setFlip(flip = true) {
    const overlay = document.getElementById('pixi-overlay');
    if (flip && !this.isFlip) {
      if (overlay) {
        overlay.style.transform = 'scaleX(-1)';
        this.isFlip = true;
      }
    }
    if (!flip && this.isFlip) {
      if (overlay) {
        overlay.style.transform = '';
        this.isFlip = false;
      }
    }
  }

  getFlip(): boolean {
    return this.isFlip;
  }
}
