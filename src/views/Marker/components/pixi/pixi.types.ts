import { Application, Container, Graphics, Rectangle } from 'pixi.js';
import * as PIXI from 'pixijs';

export interface LabelsDTO {
  Version: string;
  GroupModel: Group;
}

// 接口返回的格式
export interface Group {
  Color: string;
  Groups: Group[];
  Labels: Marker[];
  Name: string;
  AdditionalInformation?: any;
}

export type ContainerType = Container | PIXI.Container;

export interface AreasMap {
  // 原位
  DCIS_Area?: ContainerType;
  DCIS_Positive_Area?: ContainerType;
  DCIS_Negative_Area?: ContainerType;
  // 浸润
  Invasive_Area?: ContainerType;
  Invasive_Positive_Area?: ContainerType;
  Invasive_Negative_Area?: ContainerType;
  // ROI
  ROI?: ContainerType;
}

export enum TYPES {
  btn_rect = 'btn_rect',
  btn_pen = 'btn_pen',
  btn_elips = 'btn_elips',
  btn_arrow = 'btn_arrow',
  btn_brush = 'btn_brush',
  btn_ruler = 'btn_ruler',
  btn_text = 'btn_text',
  btn_point = 'btn_point',
}

export interface Marker {
  Area: number;
  Coordinates: Array<{ X: number; Y: number }>;
  Description: string;
  FontColor: string;
  FontSize: number;
  ID: string | number;
  LineColor: string;
  LineWidth: number;
  Name: string | null;
  Opacity: number;
  PartOfGroup: string;
  Tag: string;
  Type: TYPES;
  FillBackgroundColor: string;
  // attrs: any
  // remarkValue: any
}

export interface PixiAIMarkerOptions {
  showAll?: boolean; // 是否绘制结束之后展示所有区域
  pixiIsV8?: boolean; // 是否启用8版本
}

export interface IHCResult {
  dcis_n: number; //原位癌区域--阴性肿瘤细胞数量
  dcis_p: number; // 原位癌区域--阳性肿瘤细胞数量
  dcis_score: number; // 原位癌区域--数据分析百分比
  ic_n: number; // 浸润癌区域--阴性肿瘤细胞数量
  ic_p: number; // 浸润癌区域--阳性肿瘤细胞数量
  ic_score: number; // 浸润癌区域--数据分析百分比
  roi_score: number; // ROI--数据分析百分比
  total_n: number; // ROI--阴性肿瘤细胞数量
  total_p: number; // ROI--阳性肿瘤细胞数量
  wsi_score: number; //全部肿瘤区域--数据分析百分比
}

export enum CancerType {
  Artwor = 'Artwor', //原图
  All = 'All', //全部
  Positive_Area = 'Positive_Area', //阳性细胞
  Negative_Area = 'Negative_Area', //阴性细胞
  Carcinoma = 'DCIS_Area', //原位癌
  DCIS_Positive_Area = 'DCIS_Positive_Area', //原位癌阳性细胞
  DCIS_Negative_Area = 'DCIS_Negative_Area', //原位癌阳性细胞
  Infiltrating = 'Invasive_Area', //浸润癌区域
  Invasive_Positive_Area = 'Invasive_Positive_Area', //浸润癌阳性细胞
  Invasive_Negative_Area = 'Invasive_Negative_Area', //浸润癌阴性细胞
  Roi = 'ROI', //Roi
}
