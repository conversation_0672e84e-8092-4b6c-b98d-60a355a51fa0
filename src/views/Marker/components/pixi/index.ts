import OpenSeadragon, { MouseTracker } from 'openseadragon';
import Pixi<PERSON>verlay from './pixiOverlay';
import PixiMarker from './pixiMarker';

interface PixiAIMarkerOptions {
  showAll?: boolean; // 是否绘制结束之后展示所有区域
  pixiIsV8?: boolean; // 是否启用8版本
}

export default class PixiAIMarker {
  pixiOverlay: PixiOverlay;
  pixiMarker: PixiMarker;
  constructor(
    viewer: OpenSeadragon.Viewer,
    options: PixiAIMarkerOptions = {
      showAll: true,
      pixiIsV8: false,
    }
  ) {
    this.pixiOverlay = new PixiOverlay(viewer, options);
    if (options.pixiIsV8) {
      setTimeout(() => {
        this.pixiMarker = new PixiMarker(viewer, this.pixiOverlay.container, options);
      }, 2000);
    } else {
      this.pixiMarker = new PixiMarker(viewer, this.pixiOverlay.container, options);
    }
  }
}
