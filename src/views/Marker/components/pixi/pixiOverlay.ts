// pixi的图层
import OpenSeadragon, { MouseTracker, Point } from 'openseadragon';
import * as PIXI from 'pixijs';
import { Application, Container, Rectangle } from 'pixi.js';
import { AreasMap } from './pixi.types';
interface Viewport extends OpenSeadragon.Viewport {
  _containerInnerSize: { x: number; y: number };
  _contentSize: { x: number; y: number };
}

interface Options {
  showAll?: boolean; // 是否绘制结束之后展示所有区域
  pixiIsV8?: boolean; // 是否启用8版本
}

export default class PixiOverlay {
  protected _viewer: OpenSeadragon.Viewer;
  protected _canvas!: HTMLCanvasElement;
  protected _containerWidth: number = 0;
  protected _defaultContainerWidth: number = 0; //第一次加载切片，容器的宽度
  protected _containerHeight: number = 0;
  // protected _id: string = 'pixi-overlay';
  protected _id: string = 'AI-overlay';
  protected _scale: number = 0;
  protected _mouseTracker!: MouseTracker;
  protected _app: Application | PIXI.Application;
  container: Container | PIXI.Container;
  protected _drawDoneCallback: any;
  protected _options: Options;
  areasMap: AreasMap = {};
  //openseadrogen Viewer对象
  get viewer() {
    return this._viewer;
  }

  //openseadrogen viewport对象
  get viewport(): Viewport {
    return this._viewer?.viewport as Viewport;
  }

  get canvasDiv(): HTMLCanvasElement {
    return this._canvas;
  }

  constructor(viewer: OpenSeadragon.Viewer, options: Options) {
    this._viewer = viewer;
    this._containerWidth = this._viewer.container.clientWidth;
    this._containerHeight = this._viewer.container.clientHeight;
    this._defaultContainerWidth = this.viewport._containerInnerSize.x;
    this._options = options;
    this.createPixis();
    this.resize();
    this._viewer.addHandler('update-viewport', (ev) => {
      this.resize();
    });
  }

  //
  destroy() {
    this.container?.destroy(true);
    this.canvasDiv.remove();
  }

  //创建画图层div
  createCanvas() {
    // const imageBrowser = document.getElementById('image-broswer-main');
    // const konvaOverlay = imageBrowser?.querySelector(`#${this._id}`);
    const konvaOverlay = this._viewer.canvas.querySelector(`#${this._id}`);
    if (konvaOverlay) {
      this._canvas = konvaOverlay as HTMLCanvasElement;
    } else {
      this._canvas = document.createElement('canvas');
      this._canvas.style.position = 'absolute';   
      this._canvas.style.left = '0';
      this._canvas.style.top = '0';
      this._canvas.style.width = '100%';
      this._canvas.style.height = '100%';
      this._canvas.style.pointerEvents = 'none';
      this._canvas.style.zIndex = '2';
      this._canvas.setAttribute('id', this._id);
      this._viewer.canvas.appendChild(this._canvas);
    }
  }

  //设置pixi对象
  createPixis() {
    this.createCanvas();
    if (this._options?.pixiIsV8) {
      this._app = new Application();
      (async () => {
        await (this._app as Application).init({
          canvas: this._canvas,
          width: this._canvas.width,
          height: this._canvas.height,
          resizeTo: this._viewer.container,
          autoDensity: true,
          backgroundAlpha: 0,
          antialias: true,
        });
        this.container = new Container();
        (this._app as Application).stage.addChild(this.container);
        this.container.cursor = 'pointer';
        this.container.sortableChildren = true;
        this.container.interactiveChildren = false;
        this.container.hitArea = new Rectangle(
          0,
          0,
          (this._app as Application).renderer.canvas.width,
          (this._app as Application).renderer.canvas.height
        );
        console.log('await');
      })();
    } else {
      this._app = new PIXI.Application({
        view: this._canvas,
        width: this._canvas.width,
        height: this._canvas.height,
        resizeTo: this._viewer.container,
        autoDensity: true,
        backgroundAlpha: 0,
        antialias: true,
      });
      this.container = new PIXI.Container();
      this._app.stage.addChild(this.container);
      this.container.cursor = 'pointer';
      this.container.sortableChildren = true;
      this.container.interactiveChildren = false;
      this.container.hitArea = new Rectangle(
        0,
        0,
        this._app.renderer.view.width,
        this._app.renderer.view.height
      );
      this.container.filters = null;
    }
  }

  //视图大小调整
  resize() {
    const viewer = this._viewer;
    const viewport = this._viewer.viewport as Viewport;
    const app = this._app;
    var p = viewport.pixelFromPoint(new OpenSeadragon.Point(0, 0), true);
    const zoom = (viewport._containerInnerSize.x / 1000) * viewport.getZoom(true);
    const rotation = viewer.viewport.getRotation();
    if (!app.stage) return;
    app.stage.scale.set(zoom, zoom);
    app.stage.position.set(p.x, p.y);
    app.stage.rotation = rotation * (Math.PI / 180);
    app.render();
  }

  private _positionGraphicsId: string = '';
  private _positionGraphics: any = null;
  positioningOSDByPixiPoint(
    x: number,
    y: number,
    rectWidth = 100,
    rectHeight = 100,
    needSuitableZoom = true
  ) {
    const id = `${x}${y}${rectWidth}${rectHeight}`;
    if (this._positionGraphicsId === id && this._positionGraphics) {
      this._positionGraphicsId = '';
      this._positionGraphics.destroy();
      this._positionGraphics = null;
      return;
    }
    if (this._positionGraphics) {
      this._positionGraphics.destroy();
      this._positionGraphics = null;
    }

    // x y 转成视图坐标
    const panPoint = this.viewer.viewport.imageToViewportCoordinates(
      x + rectWidth / 2,
      y + rectHeight / 2
    );
    this.viewer.viewport.panTo(panPoint);

    // 转换宽高
    const point = this.viewer.viewport.imageToViewportCoordinates(x, y);
    const resultWidth =
      this.viewer.viewport.imageToViewportCoordinates(x + rectWidth, y).x - point.x;
    const resultHeight =
      this.viewer.viewport.imageToViewportCoordinates(x, y + rectHeight).y - point.y;
    this._positionGraphics = new PIXI.Graphics();
    this._positionGraphicsId = id;
    this._positionGraphics.beginFill(0xff0044, 0.1);
    this._positionGraphics.lineStyle({
      color: 16711680,
      width: 0.1,
    });
    this._positionGraphics.drawRect(
      point.x * 1000,
      point.y * 1000,
      resultWidth * 1000,
      resultHeight * 1000
    );
    (this.container as any).addChild(this._positionGraphics as any);

    if (needSuitableZoom) {
      // 获取图像最大宽高
      const tiledImage = this._viewer.world.getItemAt(0); // 假设只有一张图像
      const { x: maxWidth, y: maxHeight } = tiledImage.getContentSize(); // 图像的实际尺寸（宽和高）
      // const
      const minViewport = Math.min(maxWidth, maxHeight);
      const minRect = Math.min(resultWidth * 1000, resultHeight * 1000);
      const maxZoom = this.viewer.viewport.getMaxZoom();
      const minZoom = this.viewer.viewport.getMinZoom();
      const zoom = minViewport / minRect / 100;
      if (zoom > maxZoom) {
        this.viewer.viewport.zoomTo(maxZoom);
      } else if (zoom < minZoom) {
        this.viewer.viewport.zoomTo(minZoom);
      } else {
        this.viewer.viewport.zoomTo(zoom);
      }
    }
  }

  private _infoGraphics: any = null;
  private _infoGraphicsId: string = '';
  showHKInfo(infoParams: {
    x: number;
    y: number;
    rectWidth: number;
    rectHeight: number;
    stringArray: string[];
  }) {
    // 在矩形上添加info展示框
    const { x, y, rectWidth = 100, rectHeight = 100, stringArray = [] } = infoParams;
    const id = `${x}${y}${rectWidth}${rectHeight}`;
    if (this._infoGraphics && this._infoGraphicsId === id) {
      this._infoGraphics.destroy();
      this._infoGraphics = null;
      this._infoGraphicsId = '';
      return;
    }
    if (this._infoGraphics) {
      this._infoGraphics.destroy();
      this._infoGraphics = null;
    }

    // 转换宽高
    this._infoGraphicsId = id;
    const point = this.viewer.viewport.imageToViewportCoordinates(x, y);
    const resultWidth =
      this.viewer.viewport.imageToViewportCoordinates(x + rectWidth, y).x - point.x;
    const resultHeight =
      this.viewer.viewport.imageToViewportCoordinates(x, y + rectHeight).y - point.y;
    
    this._infoGraphics = new PIXI.Graphics();
    this._infoGraphics.beginFill(0, 0.5);
    this._infoGraphics.drawRect(
      point.x * 1000 - resultWidth * 1000 * 0.2,
      (point.y - 0.6 * resultHeight) * 1000,
      resultWidth * 1000 * 1.4,
      resultHeight * 1000 * 0.5
    );
    // this._infoGraphics.width = resultWidth * 1000;
    // this._infoGraphics.height = resultHeight * 1000 * 0.3;
    (this.container as any).addChild(this._infoGraphics as any);
    if (stringArray.length > 0) {
      const resultString = stringArray.reduce((acc, cur) => {
        return `${acc}\n${cur}`;
      }, '');
      const fontSize = resultHeight * 1000 * 0.5 * 0.4;
      const text = new PIXI.Text(resultString, {
        fontSize: 64,
        fill: 0xffffff,
        align: 'center',
      });
      text.anchor.set(0.5, 0);
      text.scale.set(fontSize / 64);
      (this._infoGraphics as any).addChild(text);

      text.x = point.x * 1000 + (resultWidth * 1000) / 2;
      text.y = (point.y - 0.6 * resultHeight) * 1000 - (resultHeight * 1000 * 0.5) / 2;
    }
  }

  // 通过data.value里面的id去查nameList里面对应的name,然后把namepush进data.value

  // data.value.map(itemX => {
  //   return {
  //     ...itemX,
  //     name: nameList.value.find(itemY = > {
  //       itemY.id === itemX.adv_activity_id
  //     })?.name
  //   }
  // })

  getPointFromPixi(point: { x: number; y: number }) {
    const { x, y } = point;
    // x = (pixel.x - pixelNoRotate.x) / this.layerScale  --- 这是将鼠标坐标转为Konva坐标点的公式
    // 求pixel.x公式：pixel.x = x * scale + pixelNoRotate.x
    const pixelNoRotate = this._viewer.viewport.pixelFromPointNoRotate(
      new OpenSeadragon.Point(0, 0),
      true
    );
    const scaleX = this._app.stage.scale.x;
    const scaleY = this._app.stage.scale.y;
    const pixelX = x * scaleX + pixelNoRotate.x;
    const pixelY = y * scaleY + pixelNoRotate.y;
    return this._viewer.viewport.pointFromPixel(new Point(pixelX, pixelY));
    // ！重点是算出形状在OSD上的Point
  }

  showContainer() {
    this.container.visible = true;
  }
  hideContainer() {
    this.container.visible = false;
  }
}
