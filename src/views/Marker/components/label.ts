/**
 * 标注的CRUD
 */
import { Layer } from './konvaOverlayer';
import { DrawCname } from './editorEnum';
import { Editor } from './editor';
import { Transformer } from 'konva/lib/shapes/Transformer';
import { LabelsDTO } from '@/views/Marker/components/pixi/pixi.types';
import { localJSONToRadingSoftwareJSON } from '@/utils/JSON';

// 接口需要的数据类型
interface KonvaMarker {
  name: string;
  id: number;
  children?: any[];
  index: number;
  attrs: any;
  node: any;
  cName?: string;
  additionalInformation?: string;
}

class Label {
  private static instance: Label | null = null;
  protected _labels: KonvaMarker[] = [];
  protected labelsDTO: LabelsDTO = {
    Version: 'V2.1',
    GroupModel: {
      Name: 'root',
      Color: '',
      Groups: [],
      Labels: [],
      AdditionalInformation: null,
    },
  };
  protected _konveEditor: Editor;
  protected observers: LabelObserver[] = [];

  constructor() {}

  // 获取所有标注
  get labels(): KonvaMarker[] {
    return this._labels;
  }

  // item只需要是konva的Shape和Group 目前没空加type
  addLabel(item: any): void {
    this._labels.push(this._handleNodeToMarker(item));
    this.notifyAllObservers();
  }

  editLabel(item: any): void {
    const index = this._labels.findIndex((e) => e.id === item.id);
    if (index !== -1) {
      this._labels[index] = {
        ...this._labels[index],
        ...item,
      };
      this.notifyAllObservers();
    }
  }

  setLabelAttrs(id: number | string, config: any) {
    const index = this._labels.findIndex((e) => e.id === id);
    if (index !== -1) {
      const label = this._labels[index];
      const { attrs } = label;
      this._labels[index].attrs = { ...attrs, ...config };
      this.notifyAllObservers();
    }
  }

  delLabel(item: any): void {
    const index = this._labels.findIndex((e) => e.id === item.id);
    if (index !== -1) {
      this._labels.splice(index, 1);
      this.notifyAllObservers();
    } else {
      console.error(`没有找到对应的标注删除，当前标注ID为:${item.id}`);
      console.log('labels为:', this._labels);
    }
  }

  delLabels(ids: string[]): void {
    this._labels = this._labels.filter((e: any) => !ids.includes(e.id));
    this.notifyAllObservers();
  }

  getLabels(): KonvaMarker[] {
    return this._labels;
  }

  editLabelsByGroup(oriGroupName: string, newGroupName: string) {
    if (!oriGroupName) return;
    this._labels = this._labels.map((item: any) => {
      const { attrs } = item;
      const { group, parentGroup } = attrs;
      return {
        ...item,
        attrs: {
          ...attrs,
          group: group === oriGroupName ? newGroupName : group,
          parentGroup: parentGroup === oriGroupName ? newGroupName : parentGroup,
        },
      };
    });
    this.notifyAllObservers();
  }

  //添加事件订阅者
  attach(observer: LabelObserver) {
    this.observers.push(observer);
  }

  //
  notifyAllObservers() {
    // 不需要进行存储的标注数据类型
    // const freeData = ['ruler'];
    // this.observers.forEach((observer) => {
    //   const labels = this._labels.filter(
    //     (label) => freeData.findIndex((item) => item === label.name) === -1
    //   );
    //   observer.update(labels);
    // });

    this.observers.forEach((observer) => {
      observer.update(this._labels);
    });
  }

  // 将layer下边的所有标注设置为labels
  setLabelsByLayer(layer: Layer) {
    if (!layer) return;
    const childrens = layer.getChildren();
    const result: KonvaMarker[] = [];
    for (let item of childrens) {
      if (!(item instanceof Transformer)) {
        result.push(this._handleNodeToMarker(item));
      }
    }
    this._labels = result;
    this.notifyAllObservers();
  }
  _handleNodeToMarker(item: any): any {
    const name = item.getAttr('name');
    const marker: KonvaMarker = {
      ...item,
      name: name,
      id: item.id(),
      attrs: item.getAttrs(),
      node: item,
      index: item.index,
      children: (item as any).children || [],
      cName: DrawCname[name as keyof typeof DrawCname],
      additionalInformation: '',
    };
    // console.log(marker, 'marker')
    return marker;
  }

  getCName(name: keyof typeof DrawCname) {
    return DrawCname[name];
  }
}

interface LabelObserver {
  update: (label?: KonvaMarker[]) => void;
}

export default Label;
