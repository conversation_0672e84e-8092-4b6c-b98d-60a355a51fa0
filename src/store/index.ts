// import useLoginStore from './modules/login'
// import useRoutersStore from './modules/routers'
// import useSelectColorStore from './modules/selectColor'
// import useZjsectionStore from './modules/zjsection'
// import useModifyFormStore from './modules/modifyForm'
// import useWatchViewStore from './modules/watchView'
// import useH3cStore from './modules/h3c'
// import useYunXinStore from './modules/yunXin'
// import useLableStore from './modules/label'
// import useShareReadingStore from './modules/shareReading'
export default function useStore() {
    return {
        // login: useLoginStore(),
        // routers: useRoutersStore(),
        // selectColor: useSelectColorStore(),
        // zjsection: useZjsectionStore(),
        // watchView: useWatchViewStore(),
        // h3c: useH3cStore(),
        // yunXin: useYunXinStore(),
        // label: useLableStore(),
        // shareReading: useShareReadingStore(),
        // modifyForm: useModifyFormStore(),
    }
}
