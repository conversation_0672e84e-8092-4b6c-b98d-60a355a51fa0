// import { defineStore } from 'pinia'
// export default defineStore('selectColor', {
//     state: () => ({
//         /**选中颜色 */
//         selectColor: '#FF0000',
//         onlineOrOffline: '1',
//         /**划线宽度 */
//         strokeWidth: 1,
//     }),
//     actions: {
//         /**设置选中颜色 */
//         selectColorFn(params: string) {
//             this.selectColor = params
//         },
//         /**设置划线宽度 */
//         strokeWidthFn(params: number) {
//             this.strokeWidth = params
//         },
        
//     },
//     // persist: {
//     //     enabled: true,
//     //     strategies: [
//     //         {
//     //             key: 'li_selectColor',
//     //             storage: localStorage,
//     //             paths: [
//     //                 'onlineOrOffline',
//     //                 'isAllMeeting',
//     //                 'isMyMeeting',
//     //                 'strokeWidth',
//     //                 'isMyCase',
//     //                 'isAllCase',
//     //                 'isAllClass',
//     //                 'isMyClass',
//     //                 'currentRole',
//     //                 'isAppealMeeting',
//     //                 'isAppealCase',
//     //                 'isAppealClass',
//     //                 'isMyCreateClass',
//     //                 'createById',
//     //                 'classStatus',
//     //                 'isMeetingEnd',
//     //                 'isWatermark',
//     //                 'isCloudRecord',
//     //                 'watermarkText',
//     //                 'isShowEditInfo',
//     //                 'isMyJoinMeeting'
//     //             ]
//     //         }
//     //     ]
//     // }
// })
