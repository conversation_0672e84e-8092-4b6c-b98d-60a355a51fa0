/**
 * 截图工具类
 */
import Konva from 'konva';
import { Context } from 'konva/lib/Context';
import OpenSeadragon, { Point } from 'openseadragon';
import { MouseTracker } from 'openseadragon';
import { Layer } from 'konva/lib/Layer';
import { Stage } from 'konva/lib/Stage';
import {
  KonvaScreenshotOverlayerOptions,
  GetScreenShotOptions,
  StartOptions,
  ScaleBarOptions,
} from './screenShot';
import { getRectImagePoint, viewCoordinatesToImageCoordinates } from '@/utils/coordinate';
import dayjs from 'dayjs';
import mitt, { type Emitter } from 'mitt';
export default class KonvaScreenshotOverlayer {
  private _canvasDiv!: HTMLDivElement | null; //容器div
  private _viewer: OpenSeadragon.Viewer; //openseadragon的viewer对象
  private _id: string = 'screenshot-overlay';
  private _containerWidth: number = 0; //容器div宽
  private _containerHeight: number = 0; //容器div高
  private _konvaStage!: Stage | null; //konva根节点
  private _layer!: Layer | null; //konva图层
  private _node: any; //截图框
  private _startPoint: any; //截图框起点
  private _drawNodeEnding: boolean = false; //初次创建截图宽
  private _mouseTracker: MouseTracker | null; //鼠标（指针设备）追踪器
  private _transformer: any; //截图框选择、变换器
  private _mask: any; //遮罩
  observers: ScreenshotObserver[] = [];
  isStart: boolean = false;
  private _initWidth: number = 50; // 初始宽
  private _initHeight: number = 50; // 初始高
  private _needFixed: boolean = false; // 是否需要固定宽高
  protected konvaScreenShotOptions: KonvaScreenshotOverlayerOptions; //
  private _toolbarElement: HTMLElement | null = null;
  private _toolbarAppend: HTMLElement | null = null;
  private _toolbarAppendParent: HTMLElement | null = null;
  private _deafaultOptions: KonvaScreenshotOverlayerOptions = {
    confirmByEnter: false,
    confirmByDblclick: false,
    needMask: false,
    maskBackgroundColor: 'rgba(0, 0, 0, 0.5)',
    customizeToolbarstyles: true,
    innerWidth: 200, // 超过多少宽度就将工具栏放在截图内
    innerHeight: 200, // 超过多少高度就将工具栏放在截图内
  };
  protected emitter: Emitter<any> = mitt();
  protected on: Emitter<any>['on'];
  protected off: Emitter<any>['off'];
  protected emit: Emitter<any>['emit'];
  private _needFullscreen: boolean = false;
  private _needMarker: boolean = false;
  private _markerCanvas: HTMLCanvasElement | null = null;
  private _scaleBarOptions: ScaleBarOptions;

  constructor(_viewer: OpenSeadragon.Viewer, options: KonvaScreenshotOverlayerOptions = {}) {
    this._viewer = _viewer;
    this._containerWidth = this._viewer.container.clientWidth;
    this._containerHeight = this._viewer.container.clientHeight;
    this.konvaScreenShotOptions = {
      ...this._deafaultOptions,
      ...options,
    };
    this.createCanvas();
    this._mouseTracker = new OpenSeadragon.MouseTracker({
      element: this._canvasDiv as HTMLDivElement,
      startDisabled: true,
    });
    this._viewer.addHandler('update-viewport', (ev) => {
      this.resize();
    });
    if (options.toolbarId) {
      this._toolbarAppend = document.getElementById(options.toolbarId);
      this._toolbarAppendParent = this._toolbarAppend?.parentNode as HTMLElement;
      this._toolbarAppend!.style.display = 'none';
    }

    this.on = this.emitter.on.bind(this.emitter);
    this.off = this.emitter.off.bind(this.emitter);
    this.emit = this.emitter.emit.bind(this.emitter);
  }

  //创建截图canvas
  createCanvas() {
    //
    const el = document.querySelector(`#${this._id}`);
    if (el) {
      this._id = `screenshot-overlay-${new Date().getTime()}`;
    }
    this._canvasDiv = document.createElement('div');
    this._canvasDiv.style.position = 'absolute';
    this._canvasDiv.style.left = '0';
    this._canvasDiv.style.top = '0';
    this._canvasDiv.style.width = '100%';
    this._canvasDiv.style.height = '100%';
    this._canvasDiv.setAttribute('id', this._id);
    this._viewer?.canvas.appendChild(this._canvasDiv);
    this._konvaStage = new Konva.Stage({
      container: this._id,
      width: this._containerWidth,
      height: this._containerHeight,
    });
  }

  //创建截图遮罩层
  createMask() {
    if (this._canvasDiv) {
      this._canvasDiv.style.zIndex = '999';
    }
    this._layer = new Konva.Layer();
    this._mask = new Konva.Shape({
      width: this._containerWidth,
      height: this._containerHeight,
      sceneFunc: (context: Context, shape) => {
        //@ts-ignore
        context.fillStyle = this.konvaScreenShotOptions.needMask
          ? this.konvaScreenShotOptions.maskBackgroundColor
          : 'rgba(0,0,0,0)';
        context.fillRect(0, 0, shape.getAttr('width'), shape.getAttr('height'));
        if (this._node) {
          context.clear(this.getNodeAttrs());
        }
      },
    });
    this._layer.add(this._mask);
    this._konvaStage?.add(this._layer);
  }

  //注册事件
  addEvent() {
    //创建截图框
    this._konvaStage?.on('mousedown', (ev: any) => {
      if (!this._node) {
        const x = ev.evt.offsetX;
        const y = ev.evt.offsetY;
        this._startPoint = { x, y };
        const mainBrower = document.getElementById('image-broswer-main');
        const imageBrower = document.getElementById('image-browser');
        mainBrower?.setAttribute(
          'style',
          `height: ${document.getElementById('image-browser')?.offsetHeight + 'px'}; width: ${
            document.getElementById('image-browser')?.offsetWidth + 'px'
          }`
        );
        imageBrower?.setAttribute(
          'style',
          `height: ${document.getElementById('image-broswer-main')?.offsetHeight + 'px'};`
        );
        this.drawRect(x, y, this._initWidth, this._initHeight);
        this._toolbarElement!.style.display = 'none';
      }
    });

    //创建截图框
    let x1: any, y1: any;
    this._konvaStage?.on('mousemove', (ev: any) => {
      this.updapteToolBarPosition();
      if (!this._drawNodeEnding && this._node) {
        let { x, y, width, height } = this.getRect(this._startPoint, {
          x: ev.evt.offsetX,
          y: ev.evt.offsetY,
        });
        this.drawRect(this._startPoint.x, this._startPoint.y, width, height);
        this._toolbarElement!.style.display = 'none';
      }
    });
    //创建截图工具
    this._konvaStage?.on('mouseup', (ev: any) => {
      if (this._node && !this._drawNodeEnding) {
        this._drawNodeEnding = true;
        this.createToolBar();
        this._toolbarElement!.style.display = 'block';
      }
    });
  }

  createToolBar() {
    if (!this._toolbarElement) {
      this._toolbarElement = document.createElement('div');
      this._toolbarElement.style.position = 'absolute';
      this._toolbarElement.style.zIndex = '999';
      if (process.env.NODE_ENV === 'development') {
        this._toolbarElement.style.background = 'red';
      }
      this._toolbarElement.setAttribute('id', 'sqray-marker-screenshot-toolbar');
      this._viewer?.canvas.appendChild(this._toolbarElement);
      if (this._toolbarAppend) {
        this._toolbarAppend.style.display = 'block';
        this._toolbarElement.appendChild(this._toolbarAppend);
      }
    }

    this.updapteToolBarPosition();
    this._toolbarElement.style.display = 'block';
  }

  // 恢复自定义工具栏
  resetToolbarAppend() {
    if (this._toolbarAppend) {
      this._toolbarAppend.style.display = 'none';
      this._toolbarAppendParent?.appendChild(this._toolbarAppend);
    }
  }

  updapteToolBarPosition() {
    if (!this._toolbarElement) return;
    setTimeout(() => {
      if (!this._node) return;
      const { x, y, width, height } = this._node.getClientRect();
      const innerWidth = this.konvaScreenShotOptions.innerWidth as number;
      const innerHeight = this.konvaScreenShotOptions.innerHeight as number;
      const barIsInner = width > innerWidth && height > innerHeight;
      this._toolbarElement!.style.width = width + 'px';
      this._toolbarElement!.style.height = 'auto';
      const left = `${x + width - this._toolbarElement!.offsetWidth}px`;
      const top = barIsInner
        ? `${y + height - this._toolbarElement!.offsetHeight - 10}px`
        : `${y + height + 10}px`;
      this._toolbarElement!.style.left = left;
      this._toolbarElement!.style.top = top;
    }, 0);
  }

  //加载截图工具图片
  loadImages(sources: { [urlName: string]: string }, callback: (images: Object) => any) {
    let images = {} as any;
    let loadedImages = 0;
    let numImages = Object.keys(sources).length || 0;
    for (let src in sources) {
      images[src] = new Image(24, 24);
      images[src].onload = function () {
        if (++loadedImages >= numImages) {
          callback(images);
        }
      };
      images[src].src = sources[src];
    }
  }

  //创建截图框
  drawRect(x: number, y: number, width: number, height: number) {
    if (this._node) {
      this._node.setAttrs({
        x,
        y,
        width: this._needFixed ? this._initWidth : width,
        height: this._needFixed ? this._initHeight : height,
      });
      this._layer?.draw();
    } else {
      this._node = new Konva.Rect({
        x: x,
        y: y,
        width,
        height,
        draggable: true,
      });
      this._node.on('dragstart', () => {
        if (this._toolbarElement) {
          this._toolbarElement.style.display = 'none';
        }
      });
      this._node.on('dragend', (ev: any) => {
        this.createToolBar();
      });
      this._layer?.add(this._node);
      this.createTransformer();
      this._layer?.draw();
      // this._node.on('dblclick', () => {
      //   this.confirmClick();
      // });
    }
    this.createToolBar();
  }

  //创建截图框选择、变换器
  createTransformer() {
    this._transformer = new Konva.Transformer({
      node: this._node,
      rotateEnabled: false,
      anchorSize: 4,
      keepRatio: false,
      ...this.konvaScreenShotOptions.transformerOptions,
    });

    this._transformer.on('transformstart', () => {
      if (this._toolbarElement) {
        this._toolbarElement.style.display = 'none';
      }
    });
    //@ts-ignore
    this._transformer.on('transformend', (ev) => {
      this.createToolBar();
    });
    this._layer?.add(this._transformer);
  }

  //获取截图框的信息
  getNodeAttrs() {
    let { x, y, width, height, scaleX, scaleY } = this._node.getAttrs();
    scaleX = scaleX || 1;
    scaleY = scaleY || 1;
    return {
      x,
      y,
      width: width * scaleX,
      height: height * scaleY,
    };
  }
  //获取矩形信息
  getRect(startPoint: any, endPoint: any) {
    const width = endPoint.x - startPoint.x;
    const height = endPoint.y - startPoint.y;
    return {
      x: Math.min(startPoint.x, endPoint.x),
      y: Math.min(startPoint.y, endPoint.y),
      width,
      height,
    };
  }

  //获取截图
  getScreenShot(isFullScreen?: boolean): any {
    const _isFullScreen = typeof isFullScreen === 'boolean' ? isFullScreen : this._needFullscreen;
    if (_isFullScreen) {
      return this.fullScreenshot({
        markerCanvas: this._markerCanvas as HTMLCanvasElement,
      });
    } else {
      return this.rectScreenShot({ markerCanvas: this._markerCanvas as HTMLCanvasElement });
    }
  }

  rectScreenShot({ markerCanvas }: { markerCanvas?: HTMLCanvasElement }) {
    const tempCanvas = document.createElement('canvas');
    const context = tempCanvas.getContext('2d') as CanvasRenderingContext2D;
    let { x, y, width, height } = this.getNodeAttrs();
    if (this._konvaStage) {
      x *= this._konvaStage?.bufferCanvas?.pixelRatio;
      y *= this._konvaStage?.bufferCanvas?.pixelRatio;
      width *= this._konvaStage?.bufferCanvas?.pixelRatio;
      height *= this._konvaStage?.bufferCanvas?.pixelRatio;
    }
    if (width <= 0 || height <= 0) {
      width = Math.abs(width);
      height = Math.abs(height);
      x -= width;
      y -= height;
    }
    tempCanvas.setAttribute('width', String(Math.abs(width)));
    tempCanvas.setAttribute('height', String(Math.abs(height)));
    context.drawImage(
      this._viewer?.drawer.canvas as any,
      x,
      y,
      width,
      height,
      0,
      0,
      Math.abs(width),
      Math.abs(height)
    );

    if (markerCanvas) {
      // 绘制标记
      context.drawImage(markerCanvas, x, y, width, height, 0, 0, Math.abs(width), Math.abs(height));
    }
    this.addScaleBar(tempCanvas);
    return {
      image: tempCanvas.toDataURL('image/png'),
      width: Math.abs(width),
      height: Math.abs(height),
      x,
      y,
    };
  }
  // 全屏截图
  fullScreenshot({ markerCanvas }: { markerCanvas?: HTMLCanvasElement }) {
    const OSDCanvas = this._viewer?.drawer.canvas as HTMLCanvasElement;
    const OSDCanvasWidth = OSDCanvas.width;
    const OSDCanvasHeight = OSDCanvas.height;
    const tempCanvas = document.createElement('canvas');
    const context = tempCanvas.getContext('2d') as CanvasRenderingContext2D;
    tempCanvas.setAttribute('width', String(Math.abs(OSDCanvasWidth)));
    tempCanvas.setAttribute('height', String(Math.abs(OSDCanvasHeight)));

    // openseadragon的绘制
    context.drawImage(
      OSDCanvas as any,
      0,
      0,
      OSDCanvasWidth,
      OSDCanvasHeight,
      0,
      0,
      Math.abs(OSDCanvasWidth),
      Math.abs(OSDCanvasHeight)
    );

    if (markerCanvas) {
      // 绘制标记
      context.drawImage(
        markerCanvas,
        0,
        0,
        OSDCanvasWidth,
        OSDCanvasHeight,
        0,
        0,
        Math.abs(OSDCanvasWidth),
        Math.abs(OSDCanvasHeight)
      );
    }
    this.addScaleBar(tempCanvas);
    return {
      image: tempCanvas.toDataURL('image/png'),
      width: Math.abs(OSDCanvasWidth),
      height: Math.abs(OSDCanvasHeight),
      x: 0,
      y: 0,
    };
  }

  //开启截图模式
  start(options: StartOptions = {}) {
    const {
      needFullScreen = false, // 默认全屏截图(无拖拽框)
      needMarker = false,
      markerCanvas = null, // 默认值为#konva-overlay canvas
      needFixed = false, // 默认不固定尺寸
      needShot = false, // 默认不立即截图（有拖拽框）
      width = 200, // 默认宽度 200
      height = 200, // 默认高度与宽度相同（如果未指定 height）
    } = options;
    this._needFullscreen = needFullScreen;
    this._needMarker = needMarker;
    this._markerCanvas = markerCanvas;

    if (needMarker && !markerCanvas) {
      this._markerCanvas = document.querySelector('#konva-overlay canvas') as HTMLCanvasElement;
    }

    if (needFullScreen) {
      this.confirmScreenshot(false);
      return;
    }

    if (this.isStart) return;

    this._mouseTracker?.setTracking(true);
    this._viewer?.setMouseNavEnabled(false);
    this._drawNodeEnding = false;
    this.createMask();
    this.addEvent();
    this.isStart = true;

    this._needFixed = needFixed;
    if (needFixed) {
      this._initWidth = width;
      this._initHeight = height;
    } else {
      this._initWidth = 50;
      this._initHeight = 50;
    }
    if (needShot) {
      this.screenshotBySized({
        width,
        height,
      });
    }
    (this._konvaStage as Stage).container().style.cursor = 'pointer';
    this.emit('startStatusChange', {
      status: this.isStart,
    });
  }

  //关闭截图模式
  close() {
    if (this._mouseTracker && this._konvaStage) {
      this._mouseTracker.setTracking(false);
      this._viewer?.setMouseNavEnabled(true);
    }
    if (this._layer) {
      this._layer.destroy();
      this._node = null;
    }
    this.observers = [];
    if (this._canvasDiv) {
      this._canvasDiv.style.zIndex = '1';
    }
    this.isStart = false;
    this.hanldeShotResize();
    if (this._toolbarElement) {
      this._toolbarElement.style.display = 'none';
    }
    (this._konvaStage as Stage).container().style.cursor = 'default';
    this.emit('startStatusChange', {
      status: this.isStart,
    });
  }

  setScaleBarOptions(options: ScaleBarOptions) {
    const { needScaleBar = false, scalesWidth = 100, scale = 20, scaleUnit = 'mm' } = options;
    this._scaleBarOptions = {
      needScaleBar,
      scalesWidth,
      scale,
      scaleUnit,
    };
  }

  addScaleBar(canvas: HTMLCanvasElement) {
    const ctx = canvas.getContext('2d') as CanvasRenderingContext2D;
    const { needScaleBar, scalesWidth, scale, scaleUnit } = this._scaleBarOptions;
    const imgWidth = canvas.width;
    const imgHeight = canvas.height;
    const unit = canvas.height;
    if (!needScaleBar) return;

    if (imgWidth < 50 || imgHeight < 50) return;

    // 1. 计算每像素对应的实际长度（μm）
    const baseUm = scaleUnit === 'um' ? scale : scale * 1000;
    const umPerPixel = baseUm / scalesWidth!;
    console.log(baseUm, umPerPixel, 'baseUm');

    // 2. 动态计算比例尺最大/最小长度（基于截图尺寸）
    const maxScaleWidth = imgWidth * 0.15; // 比例尺最大宽度=截图宽20%
    const minScaleWidth = Math.min(20, maxScaleWidth); // 最小宽度20px（保证可见性）

    // 3. 计算理想比例尺长度（像素）
    let targetPixels = scalesWidth || 0;
    if (targetPixels > maxScaleWidth) {
      targetPixels = maxScaleWidth; // 过大时缩放到最大宽度
    } else if (targetPixels < minScaleWidth) {
      targetPixels = minScaleWidth; // 过小时扩展到最小宽度
    }

    // 4. 计算实际长度（μm）并转换为易读单位
    const realLengthUm = targetPixels * umPerPixel;
    

    // 5. 优化比例尺长度（取整为整齐数值）
    const roundedValue = this.roundToNiceNumber(realLengthUm);
    const { value, unit: resultUnit } = this.normalizeUnit(roundedValue);
    const finalPixels = (roundedValue * this.unitConversion(unit as any)) / umPerPixel;
    console.log(roundedValue, this.unitConversion(unit as any), finalPixels, 'finalPixels');
    this.drawScaleBar(ctx, finalPixels, `${value}${resultUnit}`, imgWidth, imgHeight);
  }

  // 单位转换（um → 更大单位）
  normalizeUnit(lengthUm: number) {
    if (lengthUm >= 1000) {
      return { value: lengthUm / 1000, unit: 'mm' }; // 转毫米
    }
    return { value: lengthUm, unit: 'um' }; // 保持微米
  }

  // 比例尺长度取整（1, 2, 5的倍数）
  roundToNiceNumber(num: number) {
    const powers = [1, 2, 2.5, 5];
    const magnitude = Math.pow(10, Math.floor(Math.log10(num)));
    const scaled = num / magnitude;

    for (const p of powers) {
      if (scaled <= p) return p * magnitude;
    }
    return 5 * magnitude; // 默认取5的倍数
  }

  // 单位到微米的转换系数
  unitConversion(unit: 'mm' | 'um') {
    return unit === 'mm' ? 1000 : 1;
  }

  // 绘制比例尺
  drawScaleBar(
    ctx: CanvasRenderingContext2D,
    widthPx: any,
    label: any,
    imgWidth: any,
    imgHeight: any
  ) {
    // 1. 自适应尺寸计算
    const minVisibleSize = 50; // 图片最小有效尺寸
    const isSmallImage = imgWidth < minVisibleSize || imgHeight < minVisibleSize;

    // 2. 动态计算边距和位置
    const marginFactor = isSmallImage ? 0.1 : 0.03;
    const marginX = Math.max(5, imgWidth * marginFactor);
    const marginY = Math.max(5, imgHeight * marginFactor);

    // 3. 智能位置选择 (防止被截断)
    let lineY, textY;
    if (imgHeight - marginY > minVisibleSize) {
      // 优先放在底部
      lineY = imgHeight - marginY - 10;
      textY = lineY - 15;
    } else {
      // 底部空间不足时放在顶部
      lineY = marginY + 40;
      textY = lineY - 15;
    }

    // 4. 自适应字体大小
    const fontSize = Math.max(8, Math.min(12, imgWidth * 0.03));
    ctx.font = `${fontSize}px Arial, sans-serif`;

    // 5. 防止比例尺超出画布
    const maxAllowedWidth = imgWidth - marginX * 2;
    const actualWidth = Math.min(widthPx, maxAllowedWidth);

    // 6. 动态调整水平位置
    let lineX = marginX;
    if (marginX + actualWidth > imgWidth - 10) {
      lineX = imgWidth - actualWidth - marginX;
    }

    // 7. 绘制比例尺
    ctx.beginPath();
    ctx.lineWidth = isSmallImage ? 1 : 2;
    ctx.strokeStyle = 'red';
    ctx.moveTo(lineX, lineY);
    ctx.lineTo(lineX + actualWidth, lineY);
    ctx.stroke();

    // ctx.beginPath();
    // ctx.lineWidth = isSmallImage ? 1 : 2;
    // ctx.strokeStyle = '#ccc';
    // ctx.moveTo(lineX, lineY);
    // ctx.lineTo(lineX + 1, lineY + 1);
    // ctx.stroke();

    // 8. 智能文字背景
    const textWidth = ctx.measureText(label).width;
    const textX = lineX + (actualWidth - textWidth) / 2;

    // 9. 文字位置防溢出
    const adjustedTextX = Math.max(marginX, Math.min(textX, imgWidth - textWidth - marginX));

    // 10. 绘制文字背景
    if (!isSmallImage) {
      ctx.fillStyle = 'rgba(0, 0, 0, 0.6)';
      ctx.fillRect(adjustedTextX - 3, textY - 2, textWidth + 6, fontSize + 4);
    }

    // 11. 绘制文字
    ctx.fillStyle = '#fff';
    ctx.textBaseline = 'top';
    ctx.fillText(label, adjustedTextX, textY);
  }

  downloadImg() {
    const nowDate = new Date();
    let el: any = document.createElement('a');
    el.setAttribute('href', this.getScreenShot());
    el.setAttribute(
      'download',
      `${dayjs().format('YYYY-MM-DD HH:mm:ss')}.png`
      // nowDate.getFullYear() +
      //   '' +
      //   (nowDate.getMonth() + 1) +
      //   nowDate.getDate() +
      //   nowDate.getHours() +
      //   nowDate.getMinutes() +
      //   nowDate.getSeconds() +
      //   '.png'
    );
    el.click();
    el = null;
  }

  cancelScreenshot() {
    // this.notifyAllObservers();
    this.close();
  }

  confirmScreenshot(needClose = true) {
    this.notifyAllObservers(this.getScreenShot());
    if (needClose) {
      this.close();
    }
  }

  // 获取框选范围的x,y,width,height 基于矩阵左上角
  getScreenshotImgRect() {
    if (this._node) return;
    let { x, y, width, height } = this._node.getAttrs();
    const rectImagePoints = getRectImagePoint(this._viewer, { x, y, width, height });
    // 左上角坐标
    const imageX = rectImagePoints[0].imageX;
    const imageY = rectImagePoints[0].imageY;
    // 右下角坐标
    const imageRBX = rectImagePoints[2].imageX;
    const imageRBY = rectImagePoints[2].imageY;
    // 矩形的图像坐标系的宽高
    const imageWidth = imageRBX - imageX;
    const imageHeight = imageRBY - imageY;
    // 获取图像最大宽高
    const tiledImage = this._viewer.world.getItemAt(0); // 假设只有一张图像
    const { x: maxWidth, y: maxHeight } = tiledImage.getContentSize(); // 图像的实际尺寸（宽和高）

    // 是否在矩阵内
    const isIntersect = rectImagePoints.some((point) => {
      return (
        point.imageX > 0 && point.imageX < maxWidth && point.imageY > 0 && point.imageY < maxHeight
      );
    });
    if (!isIntersect) {
      return false;
    }
    return this.adjustRectanglePosition(
      {
        x: imageX,
        y: imageY,
        width: imageWidth,
        height: imageHeight,
      },
      {
        x: 0,
        y: 0,
        width: maxWidth,
        height: maxHeight,
      }
    );
  }

  /**
   * 对阵2个矩阵 矩阵A不能超出矩阵B 返回对应的x y width height
   * 说白就是求2个矩阵的交集矩阵
   * @param A
   * @param B
   * @returns
   */
  adjustRectanglePosition(A: any, B: any) {
    let { x: xA, y: yA, width: widthA, height: heightA } = A;
    let { x: xB, y: yB, width: widthB, height: heightB } = B;

    // 调整矩形 A 的位置，使其不超出矩形 B 的边界
    xA = Math.max(xA, xB);
    yA = Math.max(yA, yB);

    // 3. 确保矩形 A 不超出矩形 B 的右边界
    widthA = Math.min(widthA, widthB - (xA - xB));

    // 4. 确保矩形 A 不超出矩形 B 的下边界
    heightA = Math.min(heightA, heightB - (yA - yB));

    return {
      x: this.numberToInt(xA),
      y: this.numberToInt(yA),
      width: this.numberToInt(widthA),
      height: this.numberToInt(heightA),
    };
  }

  numberToInt(num: number | string) {
    return Math.round(Number(num));
  }

  //销毁组件
  destroy() {
    this.close();
    if (this._konvaStage) {
      this._konvaStage.destroy();
    }
    this._konvaStage = null;
    this._mouseTracker = null;
    this._canvasDiv?.remove();
  }

  //添加事件订阅者
  attach(observer: ScreenshotObserver) {
    this.observers.push(observer);
  }

  //截图成功
  notifyAllObservers(screenshot?: string) {
    this.observers.forEach((observer) => {
      observer.update(screenshot);
    });
  }

  //视图大小调整
  resize() {
    if (this._containerWidth !== this._viewer?.container.clientWidth) {
      this._containerWidth = this._viewer?.container.clientWidth as number;
      (<HTMLDivElement>this._canvasDiv).style.width = this._containerWidth + 'px';
      this._konvaStage?.width(this._containerWidth);
      this._mask?.width(this._containerWidth);
      this._layer?.width(this._containerWidth);
    }

    if (this._containerHeight !== this._viewer?.container.clientHeight) {
      this._containerHeight = this._viewer?.container.clientHeight as number;
      (<HTMLDivElement>this._canvasDiv).style.height = this._containerHeight + 'px';
      this._konvaStage?.height(this._containerHeight);
      this._mask?.height(this._containerHeight);
      this._layer?.height(this._containerHeight);
    }
    this._layer?.draw();
  }

  // 有截图尺寸时的初始截图行为
  screenshotBySized(
    rectOptions = {
      width: 200,
      height: 200,
    }
  ) {
    if (!this._node) {
      const { width, height } = rectOptions;
      const centerX = (this._layer?.width() as number) / 2 - width / 2;
      const centerY = (this._layer?.height() as number) / 2 - height / 2;
      this._startPoint = { x: centerX, y: centerY };
      const mainBrower = document.getElementById('image-broswer-main');
      const imageBrower = document.getElementById('image-browser');
      mainBrower?.setAttribute(
        'style',
        `height: ${document.getElementById('image-browser')?.offsetHeight + 'px'}; width: ${
          document.getElementById('image-browser')?.offsetWidth + 'px'
        }`
      );
      imageBrower?.setAttribute(
        'style',
        `height: ${document.getElementById('image-broswer-main')?.offsetHeight + 'px'};`
      );
      this.drawRect(centerX, centerY, width, height);
      if (this._node && !this._drawNodeEnding) {
        this._drawNodeEnding = true;
        this.createToolBar();
      }
    }
  }

  // 返回layer的宽高
  getLayerRect() {
    return {
      width: this._viewer?.container.clientWidth,
      height: this._viewer?.container.clientHeight,
    };
  }

  // 处理截图之后openseadragon konva的宽高不是100%的问题
  hanldeShotResize() {
    const mainBrower = document.getElementById('image-broswer-main');
    const imageBrower = document.getElementById('image-browser');
    mainBrower?.setAttribute('style', `height: 100%; width: 100%`);
    imageBrower?.setAttribute('style', `height: 100%;`);
  }

  //
  confirmClick() {
    if (!this.isStart) return;
    this.notifyAllObservers(this.getScreenShot());
  }
}

//

//订阅对象
interface ScreenshotObserver {
  update: (screenshot?: string) => any;
}
