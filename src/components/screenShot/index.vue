<template>
  <div class="screen-shot-box">
    <div @click="screenshot" class="content">截图</div>
    <img :src="imgSrc" alt="" style="width: 200px; height: 200px; object-fit: contain" />
    <!-- <slot name="tool-bar"></slot> -->
    <div id="screen-shot-tool-bar">
      <div @click="confirm">确认</div>
      <div @click="cancel">取消</div>
      <div @click="download">下载</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, Ref } from 'vue';
import OpenSeadragon from 'openseadragon';
import KonvaScreenshotOverlayer from './konvaScreenshotOverlayer';

const props = defineProps({
  viewer: OpenSeadragon.Viewer,
});

const konvaScreenshotOverlayer: Ref<KonvaScreenshotOverlayer | null> = ref(null);
const isFullscreen: Ref<boolean> = ref(false);
const needMarker: Ref<boolean> = ref(true);

const confirm = () => {
  konvaScreenshotOverlayer.value?.confirmScreenshot();
  // if (!konvaScreenshotOverlayer.value?.getScreenshotImgRect()) {
  //   return console.log('所选矩阵不在切片内');
  // } else {
  //   konvaScreenshotOverlayer.value?.confirmScreenshot();
  //   // konvaScreenshotOverlayer.value?.close();
  // }
};

const cancel = () => {
  konvaScreenshotOverlayer.value?.cancelScreenshot();
};
const download = () => {
  konvaScreenshotOverlayer.value?.downloadImg();
};
const imgSrc = ref('');
const screenshot = () => {
  console.log('截图点击');
  // 截图成功的回调
  konvaScreenshotOverlayer.value?.attach({
    update: (src?: string) => {
      if (src) {
        console.log('截图成功返回:', src);
        imgSrc.value = src.image || src;
      }
      konvaScreenshotOverlayer.value?.close();
    },
  });
  konvaScreenshotOverlayer.value?.setScaleBarOptions({
    needScaleBar: true,
    scalesWidth: 100,
    scale: 25,
    scaleUnit: 'um',
  });
  konvaScreenshotOverlayer.value?.start({
    needFixed: false, // 固定尺寸
    needShot: false, //
    width: 200,
    height: 200,
    needFullScreen: false,
    needMarker: true,
  });
};

onMounted(async () => {
  if (props.viewer) {
    konvaScreenshotOverlayer.value = new KonvaScreenshotOverlayer(
      props.viewer as OpenSeadragon.Viewer,
      {
        toolbarId: 'screen-shot-tool-bar',
        needMask: false,
        innerHeight: 500,
        innerWidth: 500,
        transformerOptions: {
          borderStroke: 'black',
          anchorStroke: 'black',
          anchorFill: 'black',
        },
      }
    );
  }
});
</script>

<style lang="scss">
.screen-shot-box {
  position: absolute;
  top: 200px;
  left: 250px;
  z-index: 2099;
  .content {
    cursor: pointer;

    &:hover {
      background-color: red;
    }
  }
}
</style>
