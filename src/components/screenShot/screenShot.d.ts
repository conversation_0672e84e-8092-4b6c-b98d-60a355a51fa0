import { TransformerConfig } from 'konva/lib/shapes/Transformer';

export interface KonvaScreenshotOverlayerOptions {
  toolbarId?: string; // 要挂在到工具栏的元素ID
  confirmByEnter?: boolean; // 回车键确认
  confirmByDblclick?: boolean; // 双击确认
  needMask?: boolean;
  maskBackgroundColor?: string;
  transformerOptions?: TransformerConfig;
  customizeToolbarstyles?: boolean;
  innerWidth?: number; // 工具栏出现在框选内的宽度
  innerHeight?: number; // 工具栏出现在框选内的高度
}

export interface StartOptions {
  needFullScreen?: boolean; // 是否全屏截图
  needMarker?: boolean; // 是否需要截取标注
  markerCanvas?: HTMLCanvasElement; // 标注的canvas 默认值为#konva-overlay canvas
  needFixed?: boolean; // 固定尺寸
  needShot?: boolean; // 是否立马进行截图
  width?: number;
  height?: number;
}

export interface ScaleBarOptions {
  needScaleBar?: boolean;
  scalesWidth?: number;
  scale: number;
  scaleUnit: 'um' | 'mm';
}

export interface GetScreenShotOptions {
  isFullScreen?: boolean;
  includesMarker?: boolean;
  markerCanvans?: HTMLCanvasElement;
}
