{"Version": "V2.1", "GroupModel": {"Name": "", "Color": null, "Groups": null, "Labels": [{"Name": "ASC_US", "ID": 5279937, "Type": "btn_rect", "Coordinates": [{"X": 46667, "Y": 5374}, {"X": 46783, "Y": 5574}], "LineColor": "#E6B700", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_US", "ID": 5279938, "Type": "btn_rect", "Coordinates": [{"X": 38046, "Y": 35224}, {"X": 38348, "Y": 35439}], "LineColor": "#E6B700", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_US", "ID": 5279939, "Type": "btn_rect", "Coordinates": [{"X": 12140, "Y": 44928}, {"X": 12361, "Y": 45151}], "LineColor": "#E6B700", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_US", "ID": 5279940, "Type": "btn_rect", "Coordinates": [{"X": 62727, "Y": 41863}, {"X": 63116, "Y": 42344}], "LineColor": "#E6B700", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_US", "ID": 5279941, "Type": "btn_rect", "Coordinates": [{"X": 44918, "Y": 19424}, {"X": 45094, "Y": 19621}], "LineColor": "#E6B700", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_US", "ID": 5279942, "Type": "btn_rect", "Coordinates": [{"X": 22426, "Y": 40062}, {"X": 22730, "Y": 40219}], "LineColor": "#E6B700", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_US", "ID": 5279943, "Type": "btn_rect", "Coordinates": [{"X": 58819, "Y": 50044}, {"X": 58961, "Y": 50186}], "LineColor": "#E6B700", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_US", "ID": 5279944, "Type": "btn_rect", "Coordinates": [{"X": 50314, "Y": 23949}, {"X": 50803, "Y": 24279}], "LineColor": "#E6B700", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_US", "ID": 5279945, "Type": "btn_rect", "Coordinates": [{"X": 58707, "Y": 29706}, {"X": 58983, "Y": 29942}], "LineColor": "#E6B700", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_US", "ID": 5279946, "Type": "btn_rect", "Coordinates": [{"X": 15002, "Y": 28317}, {"X": 15349, "Y": 28665}], "LineColor": "#E6B700", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_US", "ID": 5279947, "Type": "btn_rect", "Coordinates": [{"X": 45645, "Y": 17220}, {"X": 45820, "Y": 17456}], "LineColor": "#E6B700", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_US", "ID": 5279948, "Type": "btn_rect", "Coordinates": [{"X": 53857, "Y": 29137}, {"X": 54147, "Y": 29374}], "LineColor": "#E6B700", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_US", "ID": 5279949, "Type": "btn_rect", "Coordinates": [{"X": 37863, "Y": 46451}, {"X": 38148, "Y": 46770}], "LineColor": "#E6B700", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_US", "ID": 5279950, "Type": "btn_rect", "Coordinates": [{"X": 57456, "Y": 46132}, {"X": 57922, "Y": 46419}], "LineColor": "#E6B700", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_US", "ID": 5279951, "Type": "btn_rect", "Coordinates": [{"X": 19823, "Y": 57063}, {"X": 19998, "Y": 57247}], "LineColor": "#E6B700", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_US", "ID": 5279952, "Type": "btn_rect", "Coordinates": [{"X": 15783, "Y": 16068}, {"X": 16102, "Y": 16335}], "LineColor": "#E6B700", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_US", "ID": 5279953, "Type": "btn_rect", "Coordinates": [{"X": 52857, "Y": 32438}, {"X": 53135, "Y": 32777}], "LineColor": "#E6B700", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_US", "ID": 5279954, "Type": "btn_rect", "Coordinates": [{"X": 34415, "Y": 55668}, {"X": 34589, "Y": 55843}], "LineColor": "#E6B700", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_US", "ID": 5279955, "Type": "btn_rect", "Coordinates": [{"X": 6686, "Y": 45636}, {"X": 6926, "Y": 45875}], "LineColor": "#E6B700", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_US", "ID": 5279956, "Type": "btn_rect", "Coordinates": [{"X": 56732, "Y": 46224}, {"X": 57011, "Y": 46453}], "LineColor": "#E6B700", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_US", "ID": 5279957, "Type": "btn_rect", "Coordinates": [{"X": 35727, "Y": 24794}, {"X": 35886, "Y": 24925}], "LineColor": "#E6B700", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_US", "ID": 5279958, "Type": "btn_rect", "Coordinates": [{"X": 31070, "Y": 47131}, {"X": 31374, "Y": 47345}], "LineColor": "#E6B700", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_US", "ID": 5279959, "Type": "btn_rect", "Coordinates": [{"X": 29562, "Y": 55271}, {"X": 29741, "Y": 55432}], "LineColor": "#E6B700", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_US", "ID": 5279960, "Type": "btn_rect", "Coordinates": [{"X": 35327, "Y": 34600}, {"X": 35871, "Y": 34962}], "LineColor": "#E6B700", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_US", "ID": 5279961, "Type": "btn_rect", "Coordinates": [{"X": 47816, "Y": 47992}, {"X": 48083, "Y": 48269}], "LineColor": "#E6B700", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_US", "ID": 5279962, "Type": "btn_rect", "Coordinates": [{"X": 44919, "Y": 47346}, {"X": 45280, "Y": 47670}], "LineColor": "#E6B700", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_US", "ID": 5279963, "Type": "btn_rect", "Coordinates": [{"X": 61737, "Y": 40854}, {"X": 61886, "Y": 41056}], "LineColor": "#E6B700", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_US", "ID": 5279964, "Type": "btn_rect", "Coordinates": [{"X": 43803, "Y": 57062}, {"X": 44096, "Y": 57310}], "LineColor": "#E6B700", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_US", "ID": 5279965, "Type": "btn_rect", "Coordinates": [{"X": 16084, "Y": 13642}, {"X": 16377, "Y": 13863}], "LineColor": "#E6B700", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_US", "ID": 5279966, "Type": "btn_rect", "Coordinates": [{"X": 42490, "Y": 18397}, {"X": 42699, "Y": 18616}], "LineColor": "#E6B700", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "LSIL", "ID": 5279967, "Type": "btn_rect", "Coordinates": [{"X": 37148, "Y": 17926}, {"X": 37446, "Y": 18209}], "LineColor": "#E65C00", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "LSIL", "ID": 5279968, "Type": "btn_rect", "Coordinates": [{"X": 52860, "Y": 32778}, {"X": 53108, "Y": 33101}], "LineColor": "#E65C00", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "LSIL", "ID": 5279969, "Type": "btn_rect", "Coordinates": [{"X": 32867, "Y": 43099}, {"X": 33312, "Y": 43645}], "LineColor": "#E65C00", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "LSIL", "ID": 5279970, "Type": "btn_rect", "Coordinates": [{"X": 47807, "Y": 40795}, {"X": 48163, "Y": 41269}], "LineColor": "#E65C00", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "LSIL", "ID": 5279971, "Type": "btn_rect", "Coordinates": [{"X": 6970, "Y": 34705}, {"X": 7204, "Y": 35017}], "LineColor": "#E65C00", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "LSIL", "ID": 5279972, "Type": "btn_rect", "Coordinates": [{"X": 38220, "Y": 60837}, {"X": 38351, "Y": 61134}], "LineColor": "#E65C00", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "LSIL", "ID": 5279973, "Type": "btn_rect", "Coordinates": [{"X": 31271, "Y": 15003}, {"X": 31562, "Y": 15196}], "LineColor": "#E65C00", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "LSIL", "ID": 5279974, "Type": "btn_rect", "Coordinates": [{"X": 32101, "Y": 27519}, {"X": 32613, "Y": 27809}], "LineColor": "#E65C00", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "LSIL", "ID": 5279975, "Type": "btn_rect", "Coordinates": [{"X": 22072, "Y": 16543}, {"X": 22380, "Y": 16745}], "LineColor": "#E65C00", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "LSIL", "ID": 5279976, "Type": "btn_rect", "Coordinates": [{"X": 62976, "Y": 42723}, {"X": 63127, "Y": 43021}], "LineColor": "#E65C00", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "LSIL", "ID": 5279977, "Type": "btn_rect", "Coordinates": [{"X": 36420, "Y": 29136}, {"X": 36595, "Y": 29326}], "LineColor": "#E65C00", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "LSIL", "ID": 5279978, "Type": "btn_rect", "Coordinates": [{"X": 57723, "Y": 43250}, {"X": 58155, "Y": 43542}], "LineColor": "#E65C00", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "LSIL", "ID": 5279979, "Type": "btn_rect", "Coordinates": [{"X": 64024, "Y": 36959}, {"X": 64265, "Y": 37204}], "LineColor": "#E65C00", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "LSIL", "ID": 5279980, "Type": "btn_rect", "Coordinates": [{"X": 44670, "Y": 47565}, {"X": 44917, "Y": 47881}], "LineColor": "#E65C00", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "LSIL", "ID": 5279981, "Type": "btn_rect", "Coordinates": [{"X": 43704, "Y": 8018}, {"X": 43820, "Y": 8169}], "LineColor": "#E65C00", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "LSIL", "ID": 5279982, "Type": "btn_rect", "Coordinates": [{"X": 23070, "Y": 42222}, {"X": 23294, "Y": 42489}], "LineColor": "#E65C00", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "LSIL", "ID": 5279983, "Type": "btn_rect", "Coordinates": [{"X": 62549, "Y": 39551}, {"X": 62998, "Y": 39820}], "LineColor": "#E65C00", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "LSIL", "ID": 5279984, "Type": "btn_rect", "Coordinates": [{"X": 59710, "Y": 48974}, {"X": 59917, "Y": 49194}], "LineColor": "#E65C00", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "LSIL", "ID": 5279985, "Type": "btn_rect", "Coordinates": [{"X": 49302, "Y": 15513}, {"X": 49647, "Y": 15781}], "LineColor": "#E65C00", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "LSIL", "ID": 5279986, "Type": "btn_rect", "Coordinates": [{"X": 61536, "Y": 38606}, {"X": 61700, "Y": 38755}], "LineColor": "#E65C00", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "LSIL", "ID": 5279987, "Type": "btn_rect", "Coordinates": [{"X": 64343, "Y": 30350}, {"X": 64518, "Y": 30474}], "LineColor": "#E65C00", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "LSIL", "ID": 5279988, "Type": "btn_rect", "Coordinates": [{"X": 54129, "Y": 25199}, {"X": 54464, "Y": 25493}], "LineColor": "#E65C00", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "LSIL", "ID": 5279989, "Type": "btn_rect", "Coordinates": [{"X": 50512, "Y": 49468}, {"X": 50771, "Y": 49745}], "LineColor": "#E65C00", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "LSIL", "ID": 5279990, "Type": "btn_rect", "Coordinates": [{"X": 24953, "Y": 34135}, {"X": 25164, "Y": 34407}], "LineColor": "#E65C00", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "LSIL", "ID": 5279991, "Type": "btn_rect", "Coordinates": [{"X": 12636, "Y": 16996}, {"X": 12799, "Y": 17064}], "LineColor": "#E65C00", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "LSIL", "ID": 5279992, "Type": "btn_rect", "Coordinates": [{"X": 21505, "Y": 18297}, {"X": 21736, "Y": 18556}], "LineColor": "#E65C00", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "LSIL", "ID": 5279993, "Type": "btn_rect", "Coordinates": [{"X": 52371, "Y": 22080}, {"X": 52595, "Y": 22402}], "LineColor": "#E65C00", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "LSIL", "ID": 5279994, "Type": "btn_rect", "Coordinates": [{"X": 45373, "Y": 42373}, {"X": 45600, "Y": 42489}], "LineColor": "#E65C00", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "LSIL", "ID": 5279995, "Type": "btn_rect", "Coordinates": [{"X": 41280, "Y": 24280}, {"X": 41468, "Y": 24382}], "LineColor": "#E65C00", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "LSIL", "ID": 5279996, "Type": "btn_rect", "Coordinates": [{"X": 41165, "Y": 45413}, {"X": 41275, "Y": 45577}], "LineColor": "#E65C00", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "HSIL", "ID": 5279997, "Type": "btn_rect", "Coordinates": [{"X": 66912, "Y": 30495}, {"X": 67213, "Y": 30698}], "LineColor": "#E60000", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "CC", "ID": 5279998, "Type": "btn_rect", "Coordinates": [{"X": 17700, "Y": 24434}, {"X": 17892, "Y": 24682}], "LineColor": "#661818", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_H", "ID": 5280029, "Type": "btn_rect", "Coordinates": [{"X": 43952, "Y": 38290}, {"X": 44136, "Y": 38453}], "LineColor": "#808000", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_H", "ID": 5280030, "Type": "btn_rect", "Coordinates": [{"X": 60702, "Y": 22034}, {"X": 60787, "Y": 22115}], "LineColor": "#808000", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_H", "ID": 5280031, "Type": "btn_rect", "Coordinates": [{"X": 15317, "Y": 54237}, {"X": 15503, "Y": 54436}], "LineColor": "#808000", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_H", "ID": 5280032, "Type": "btn_rect", "Coordinates": [{"X": 56138, "Y": 38723}, {"X": 56320, "Y": 38847}], "LineColor": "#808000", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_H", "ID": 5280033, "Type": "btn_rect", "Coordinates": [{"X": 47479, "Y": 44313}, {"X": 47590, "Y": 44467}], "LineColor": "#808000", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_H", "ID": 5280034, "Type": "btn_rect", "Coordinates": [{"X": 37568, "Y": 30350}, {"X": 37633, "Y": 30433}], "LineColor": "#808000", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_H", "ID": 5280035, "Type": "btn_rect", "Coordinates": [{"X": 18210, "Y": 34704}, {"X": 18292, "Y": 34811}], "LineColor": "#808000", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_H", "ID": 5280036, "Type": "btn_rect", "Coordinates": [{"X": 34844, "Y": 20638}, {"X": 34964, "Y": 20743}], "LineColor": "#808000", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_H", "ID": 5280037, "Type": "btn_rect", "Coordinates": [{"X": 57671, "Y": 51599}, {"X": 57759, "Y": 51662}], "LineColor": "#808000", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_H", "ID": 5280038, "Type": "btn_rect", "Coordinates": [{"X": 35640, "Y": 43704}, {"X": 35734, "Y": 43766}], "LineColor": "#808000", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_H", "ID": 5280039, "Type": "btn_rect", "Coordinates": [{"X": 35151, "Y": 26257}, {"X": 35205, "Y": 26380}], "LineColor": "#808000", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_H", "ID": 5280040, "Type": "btn_rect", "Coordinates": [{"X": 13101, "Y": 33650}, {"X": 13178, "Y": 33720}], "LineColor": "#808000", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_H", "ID": 5280041, "Type": "btn_rect", "Coordinates": [{"X": 48190, "Y": 34925}, {"X": 48328, "Y": 35037}], "LineColor": "#808000", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_H", "ID": 5280042, "Type": "btn_rect", "Coordinates": [{"X": 11415, "Y": 43233}, {"X": 11499, "Y": 43306}], "LineColor": "#808000", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_H", "ID": 5280043, "Type": "btn_rect", "Coordinates": [{"X": 28033, "Y": 30543}, {"X": 28225, "Y": 30675}], "LineColor": "#808000", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_H", "ID": 5280044, "Type": "btn_rect", "Coordinates": [{"X": 14136, "Y": 50944}, {"X": 14255, "Y": 50987}], "LineColor": "#808000", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_H", "ID": 5280045, "Type": "btn_rect", "Coordinates": [{"X": 56767, "Y": 15991}, {"X": 56862, "Y": 16159}], "LineColor": "#808000", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_H", "ID": 5280046, "Type": "btn_rect", "Coordinates": [{"X": 38848, "Y": 11403}, {"X": 38913, "Y": 11490}], "LineColor": "#808000", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_H", "ID": 5280047, "Type": "btn_rect", "Coordinates": [{"X": 55471, "Y": 56378}, {"X": 55574, "Y": 56548}], "LineColor": "#808000", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_H", "ID": 5280048, "Type": "btn_rect", "Coordinates": [{"X": 57631, "Y": 39047}, {"X": 57735, "Y": 39150}], "LineColor": "#808000", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_H", "ID": 5280049, "Type": "btn_rect", "Coordinates": [{"X": 58784, "Y": 18140}, {"X": 58869, "Y": 18209}], "LineColor": "#808000", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_H", "ID": 5280050, "Type": "btn_rect", "Coordinates": [{"X": 50016, "Y": 64692}, {"X": 50115, "Y": 64789}], "LineColor": "#808000", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_H", "ID": 5280051, "Type": "btn_rect", "Coordinates": [{"X": 14173, "Y": 10322}, {"X": 14485, "Y": 10575}], "LineColor": "#808000", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_H", "ID": 5280052, "Type": "btn_rect", "Coordinates": [{"X": 40925, "Y": 42630}, {"X": 41159, "Y": 42734}], "LineColor": "#808000", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_H", "ID": 5280053, "Type": "btn_rect", "Coordinates": [{"X": 35748, "Y": 57565}, {"X": 35826, "Y": 57653}], "LineColor": "#808000", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_H", "ID": 5280054, "Type": "btn_rect", "Coordinates": [{"X": 58870, "Y": 26078}, {"X": 58956, "Y": 26142}], "LineColor": "#808000", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_H", "ID": 5280055, "Type": "btn_rect", "Coordinates": [{"X": 14284, "Y": 45342}, {"X": 14397, "Y": 45424}], "LineColor": "#808000", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_H", "ID": 5280056, "Type": "btn_rect", "Coordinates": [{"X": 42577, "Y": 10214}, {"X": 42658, "Y": 10330}], "LineColor": "#808000", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_H", "ID": 5280057, "Type": "btn_rect", "Coordinates": [{"X": 57692, "Y": 26424}, {"X": 57868, "Y": 26641}], "LineColor": "#808000", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}, {"Name": "ASC_H", "ID": 5280058, "Type": "btn_rect", "Coordinates": [{"X": 19862, "Y": 42418}, {"X": 19965, "Y": 42489}], "LineColor": "#808000", "LineWidth": 3, "FontSize": 14, "FontColor": "", "Area": 0, "Description": "", "FillBackgroundColor": "", "Opacity": 1, "Tag": ""}], "AdditionalInformation": ""}}