{"Version": "V2.0", "GroupModel": {"Color": "", "Groups": [{"Color": "", "Groups": [{"Color": "", "Groups": [], "Labels": [{"Area": 4.0, "Coordinates": [{"X": 5698.3065185546875, "Y": 34075.565185546875}, {"X": 5997.0919189453125, "Y": 34007.046630859375}, {"X": 5893.898681640625, "Y": 34241.19384765625}, {"X": 5785.3245849609375, "Y": 34055.87548828125}, {"X": 5171.6331787109375, "Y": 34060.977783203125}, {"X": 5980.3558349609375, "Y": 33985.838134765625}, {"X": 5186.087158203125, "Y": 34094.61083984375}, {"X": 5748.6126708984375, "Y": 34068.897216796875}, {"X": 5745.0087890625, "Y": 34299.562744140625}, {"X": 5812.01416015625, "Y": 34256.56884765625}, {"X": 5804.7734375, "Y": 34288.005859375}, {"X": 5038.011535644531, "Y": 33829.123779296875}, {"X": 5736.36669921875, "Y": 34269.8994140625}, {"X": 5897.9132080078125, "Y": 34268.029296875}, {"X": 5815.405029296875, "Y": 34175.73828125}, {"X": 5788.15771484375, "Y": 34186.1220703125}, {"X": 5028.726257324219, "Y": 34022.34130859375}, {"X": 5003.746307373047, "Y": 33870.22705078125}, {"X": 5786.2579345703125, "Y": 34271.30029296875}, {"X": 5788.130859375, "Y": 34236.52587890625}], "Description": "", "FillBackgroundColor": "#FFc1c2bc", "FontColor": "", "FontSize": 18.0, "ID": 1, "LineColor": "", "LineWidth": 5.0, "Name": "root", "Opacity": 0.5, "PartOfGroup": "DCIS_Negative_Area", "Tag": null, "Type": "btn_point"}], "Name": "DCIS_Negative_Area"}, {"Color": "", "Groups": [], "Labels": [{"Area": 4.0, "Coordinates": [], "Description": "", "FillBackgroundColor": "#FF4e200f", "FontColor": "", "FontSize": 18.0, "ID": 1, "LineColor": "", "LineWidth": 5.0, "Name": "root", "Opacity": 0.5, "PartOfGroup": "DCIS_Positive_Area", "Tag": null, "Type": "btn_point"}], "Name": "DCIS_Positive_Area"}], "Labels": [], "Name": "DCIS_Area"}, {"Color": "", "Groups": [{"Color": "", "Groups": [], "Labels": [{"Area": 4.0, "Coordinates": [{"X": 10468.61669921875, "Y": 32522.953125}, {"X": 10784.90869140625, "Y": 32888.04345703125}, {"X": 10479.707641601562, "Y": 32488.5537109375}, {"X": 10481.760009765625, "Y": 32566.7138671875}, {"X": 10682.20947265625, "Y": 32661.6533203125}, {"X": 10739.84765625, "Y": 32873.33203125}, {"X": 10811.94580078125, "Y": 32854.644287109375}, {"X": 10823.74853515625, "Y": 33170.77099609375}, {"X": 10473.330078125, "Y": 32595.600341796875}, {"X": 10562.688232421875, "Y": 32425.66552734375}, {"X": 10658.664794921875, "Y": 32503.4443359375}, {"X": 10706.23095703125, "Y": 32512.34375}, {"X": 10659.2333984375, "Y": 32953.7236328125}, {"X": 10811.5244140625, "Y": 32905.3232421875}, {"X": 10623.55322265625, "Y": 32467.79345703125}, {"X": 10459.573486328125, "Y": 32457.85546875}, {"X": 10706.60009765625, "Y": 32652.46142578125}, {"X": 10651.9873046875, "Y": 32842.47509765625}, {"X": 10514.370971679688, "Y": 32627.85302734375}, {"X": 10845.546142578125, "Y": 33147.228515625}, {"X": 10625.23193359375, "Y": 32850.12548828125}, {"X": 10672.94580078125, "Y": 32483.3037109375}, {"X": 10761.071044921875, "Y": 32577.185546875}, {"X": 10751.76806640625, "Y": 33140.14794921875}, {"X": 10554.848876953125, "Y": 32229.7890625}, {"X": 10551.289306640625, "Y": 32640.27783203125}, {"X": 10875.08984375, "Y": 32837.11181640625}, {"X": 10744.90283203125, "Y": 32551.7919921875}, {"X": 10575.27880859375, "Y": 32636.40087890625}, {"X": 10825.259033203125, "Y": 32584.014892578125}, {"X": 10519.789306640625, "Y": 32410.702392578125}, {"X": 10767.009033203125, "Y": 32536.219970703125}, {"X": 10541.755859375, "Y": 32220.678466796875}, {"X": 10455.20654296875, "Y": 32610.291015625}, {"X": 10687.1337890625, "Y": 32520.03662109375}, {"X": 10713.887451171875, "Y": 32542.09375}, {"X": 10536.357055664062, "Y": 32384.3232421875}, {"X": 10597.20849609375, "Y": 32446.9462890625}, {"X": 10713.992919921875, "Y": 32601.3330078125}, {"X": 10630.671875, "Y": 32624.26220703125}, {"X": 10779.48046875, "Y": 32918.1533203125}, {"X": 10644.97998046875, "Y": 32658.5966796875}, {"X": 10785.875, "Y": 32685.9306640625}, {"X": 10729.46240234375, "Y": 32559.7490234375}, {"X": 10788.76513671875, "Y": 32549.05029296875}, {"X": 10730.26220703125, "Y": 32766.837158203125}, {"X": 11387.349853515625, "Y": 33806.91015625}, {"X": 10630.06689453125, "Y": 32635.71533203125}, {"X": 5255.213562011719, "Y": 28854.4619140625}, {"X": 5264.29833984375, "Y": 28600.474609375}, {"X": 5224.600341796875, "Y": 28816.08544921875}, {"X": 5227.2557373046875, "Y": 28561.144775390625}, {"X": 5228.3240966796875, "Y": 28911.8486328125}, {"X": 5312.6624755859375, "Y": 28534.126953125}, {"X": 5206.1778564453125, "Y": 28594.62451171875}, {"X": 5105.964599609375, "Y": 28693.302978515625}, {"X": 5262.348388671875, "Y": 28507.741943359375}, {"X": 5196.5137939453125, "Y": 28866.67724609375}, {"X": 5166.6868896484375, "Y": 28856.7041015625}, {"X": 5359.894592285156, "Y": 28660.80712890625}, {"X": 5276.625244140625, "Y": 28565.71875}, {"X": 5136.9571533203125, "Y": 28873.4677734375}, {"X": 5301.82666015625, "Y": 28891.17431640625}, {"X": 5202.895812988281, "Y": 28741.76318359375}, {"X": 5188.2293701171875, "Y": 28801.62255859375}, {"X": 5260.5576171875, "Y": 28729.462646484375}, {"X": 5305.787841796875, "Y": 28676.30908203125}, {"X": 5206.5042724609375, "Y": 28657.5576171875}, {"X": 5335.2415771484375, "Y": 28522.84912109375}, {"X": 5260.1845703125, "Y": 28800.908447265625}, {"X": 5342.108154296875, "Y": 28761.70556640625}, {"X": 5352.168518066406, "Y": 28573.69677734375}, {"X": 6161.4168701171875, "Y": 33390.44140625}, {"X": 5877.763916015625, "Y": 33812.56005859375}, {"X": 5840.3817138671875, "Y": 33865.2275390625}, {"X": 7769.32421875, "Y": 30846.642944335938}, {"X": 7718.77880859375, "Y": 30846.04833984375}, {"X": 5867.909423828125, "Y": 33853.9130859375}, {"X": 6116.781005859375, "Y": 33398.8486328125}, {"X": 7780.27294921875, "Y": 30753.777465820312}, {"X": 6130.600830078125, "Y": 33416.87255859375}, {"X": 7745.989501953125, "Y": 30879.86279296875}, {"X": 7955.12158203125, "Y": 30801.10174560547}, {"X": 5024.568176269531, "Y": 33139.84716796875}, {"X": 7922.22802734375, "Y": 30839.145263671875}, {"X": 7832.2392578125, "Y": 30727.424865722656}, {"X": 6089.63818359375, "Y": 33481.50244140625}, {"X": 5958.17578125, "Y": 33898.8818359375}, {"X": 7753.140380859375, "Y": 30914.52099609375}, {"X": 6095.016845703125, "Y": 33446.00244140625}, {"X": 5987.20849609375, "Y": 33857.8017578125}, {"X": 5786.2724609375, "Y": 33973.267333984375}, {"X": 5812.47314453125, "Y": 33956.206787109375}, {"X": 7734.31396484375, "Y": 30754.31707763672}, {"X": 5828.943603515625, "Y": 33890.723876953125}, {"X": 6105.0062255859375, "Y": 33658.00390625}, {"X": 7871.23974609375, "Y": 30851.206665039062}, {"X": 7976.9365234375, "Y": 30813.59619140625}, {"X": 7869.81396484375, "Y": 30874.200439453125}, {"X": 6068.126220703125, "Y": 33440.465087890625}, {"X": 7657.020751953125, "Y": 30798.24658203125}, {"X": 7968.009765625, "Y": 30835.354614257812}, {"X": 6089.517822265625, "Y": 33532.11865234375}, {"X": 7904.57177734375, "Y": 30818.02227783203}, {"X": 7865.720703125, "Y": 30897.222534179688}, {"X": 6050.2991943359375, "Y": 33496.12060546875}, {"X": 6009.132568359375, "Y": 33982.638427734375}, {"X": 7845.00146484375, "Y": 30836.149536132812}, {"X": 4932.801940917969, "Y": 33214.318359375}, {"X": 7753.83544921875, "Y": 30720.41064453125}, {"X": 7945.09619140625, "Y": 30839.353515625}, {"X": 5025.6002197265625, "Y": 33202.96728515625}, {"X": 6196.049560546875, "Y": 33358.6767578125}, {"X": 6108.35888671875, "Y": 33613.5283203125}, {"X": 6005.123046875, "Y": 33872.07177734375}, {"X": 6015.91650390625, "Y": 34197.619140625}, {"X": 5783.3223876953125, "Y": 34016.061279296875}, {"X": 7836.5634765625, "Y": 30994.290466308594}, {"X": 7898.12109375, "Y": 30979.29541015625}, {"X": 7807.197021484375, "Y": 30787.243041992188}, {"X": 7990.3896484375, "Y": 30859.060302734375}, {"X": 7857.3779296875, "Y": 30807.057983398438}, {"X": 7716.83203125, "Y": 30886.777587890625}, {"X": 7616.855712890625, "Y": 30937.61688232422}, {"X": 4970.971435546875, "Y": 33203.79150390625}, {"X": 5996.166748046875, "Y": 33480.453125}, {"X": 5192.008361816406, "Y": 33090.38134765625}, {"X": 6049.6640625, "Y": 33631.89013671875}, {"X": 5095.665222167969, "Y": 33108.4951171875}, {"X": 4985.0283203125, "Y": 33183.08740234375}, {"X": 5808.6466064453125, "Y": 33995.8876953125}, {"X": 5967.8212890625, "Y": 34190.9638671875}, {"X": 5978.425537109375, "Y": 34299.7880859375}, {"X": 5050.295837402344, "Y": 33166.0791015625}, {"X": 6037.989013671875, "Y": 33519.689453125}, {"X": 5912.4234619140625, "Y": 34162.1943359375}, {"X": 5990.314453125, "Y": 33398.66015625}, {"X": 6076.505126953125, "Y": 33561.70068359375}, {"X": 5154.5633544921875, "Y": 33087.34619140625}, {"X": 7665.697265625, "Y": 30941.77978515625}, {"X": 7808.12353515625, "Y": 30935.00341796875}, {"X": 6055.16552734375, "Y": 33966.1298828125}, {"X": 4975.903381347656, "Y": 33237.9521484375}, {"X": 5998.1766357421875, "Y": 33373.78662109375}, {"X": 5915.9483642578125, "Y": 33725.572265625}, {"X": 8002.65283203125, "Y": 30872.827514648438}, {"X": 6069.4327392578125, "Y": 33934.6416015625}, {"X": 5305.2489013671875, "Y": 33081.5322265625}, {"X": 5995.21484375, "Y": 34177.7265625}, {"X": 7825.523193359375, "Y": 30848.663146972656}, {"X": 7895.54296875, "Y": 30801.078186035156}, {"X": 5990.9246826171875, "Y": 34205.6953125}, {"X": 6049.673828125, "Y": 33581.0849609375}, {"X": 6043.68359375, "Y": 33425.626953125}, {"X": 6095.34716796875, "Y": 33798.2294921875}], "Description": "", "FillBackgroundColor": "#FFc1c2bc", "FontColor": "", "FontSize": 18.0, "ID": 1, "LineColor": "", "LineWidth": 5.0, "Name": "root", "Opacity": 0.5, "PartOfGroup": "Invasive_Negative_Area", "Tag": null, "Type": "btn_point"}], "Name": "Invasive_Negative_Area"}, {"Color": "", "Groups": [], "Labels": [{"Area": 4.0, "Coordinates": [], "Description": "", "FillBackgroundColor": "#FF4e200f", "FontColor": "", "FontSize": 18.0, "ID": 1, "LineColor": "", "LineWidth": 5.0, "Name": "root", "Opacity": 0.5, "PartOfGroup": "Invasive_Positive_Area", "Tag": null, "Type": "btn_point"}], "Name": "Invasive_Positive_Area"}], "Labels": [{"Area": 0.171, "Coordinates": ["7872.00, 37568.00", "7872.00, 37616.00", "7824.00, 37632.00", "7776.00, 37616.00", "7808.00, 37632.00", "7840.00, 37648.00", "7888.00, 37696.00", "7856.00, 37728.00", "7840.00, 37776.00", "7824.00, 37808.00", "7776.00, 37824.00", "7760.00, 37808.00", "7712.00, 37824.00", "7664.00, 37824.00", "7696.00, 37872.00", "7648.00, 37888.00", "7600.00, 37888.00", "7584.00, 37920.00", "7568.00, 37968.00", "7552.00, 38016.00", "7600.00, 38048.00", "7648.00, 38064.00", "7696.00, 38048.00", "7728.00, 38016.00", "7776.00, 38016.00", "7824.00, 38048.00", "7808.00, 38000.00", "7856.00, 37968.00", "7888.00, 37984.00", "7856.00, 38016.00", "7888.00, 38048.00", "7920.00, 38080.00", "7968.00, 38112.00", "8016.00, 38128.00", "8048.00, 38080.00", "8080.00, 38096.00", "8064.00, 38080.00", "8112.00, 38032.00", "8080.00, 38000.00", "8096.00, 37952.00", "8064.00, 37920.00", "8016.00, 37904.00", "7984.00, 37872.00", "7984.00, 37824.00", "8000.00, 37792.00", "7984.00, 37744.00", "7952.00, 37696.00", "7904.00, 37696.00", "7872.00, 37648.00", "7888.00, 37600.00", "7936.00, 37616.00", "7984.00, 37648.00", "7968.00, 37600.00", "7920.00, 37600.00", "7872.00, 37568.00"], "FillBackgroundColor": "", "FontColor": "#FFFF0000", "FontSize": 12.0, "ID": 1, "LineColor": "#FFFF0000", "LineWidth": 2.0, "Name": "root", "Opacity": 0, "PartOfGroup": "", "Tag": "add", "Type": "btn_brush"}, {"Area": 0.171, "Coordinates": ["5984.00, 37456.00", "5936.00, 37472.00", "5936.00, 37504.00", "5904.00, 37536.00", "5856.00, 37536.00", "5808.00, 37536.00", "5776.00, 37568.00", "5728.00, 37600.00", "5696.00, 37632.00", "5648.00, 37664.00", "5680.00, 37696.00", "5728.00, 37728.00", "5744.00, 37776.00", "5696.00, 37776.00", "5648.00, 37792.00", "5600.00, 37760.00", "5600.00, 37712.00", "5552.00, 37712.00", "5504.00, 37696.00", "5456.00, 37680.00", "5440.00, 37712.00", "5456.00, 37728.00", "5504.00, 37760.00", "5520.00, 37808.00", "5504.00, 37840.00", "5456.00, 37888.00", "5408.00, 37856.00", "5360.00, 37824.00", "5312.00, 37792.00", "5312.00, 37744.00", "5360.00, 37728.00", "5392.00, 37712.00", "5360.00, 37696.00", "5344.00, 37664.00", "5392.00, 37632.00", "5408.00, 37632.00", "5376.00, 37584.00", "5328.00, 37584.00", "5280.00, 37600.00", "5232.00, 37616.00", "5184.00, 37616.00", "5168.00, 37616.00", "5152.00, 37664.00", "5136.00, 37712.00", "5184.00, 37744.00", "5216.00, 37792.00", "5168.00, 37840.00", "5152.00, 37856.00", "5200.00, 37856.00", "5248.00, 37856.00", "5280.00, 37888.00", "5248.00, 37936.00", "5200.00, 37904.00", "5184.00, 37952.00", "5152.00, 38000.00", "5184.00, 38016.00", "5232.00, 38016.00", "5280.00, 38016.00", "5264.00, 37984.00", "5296.00, 37952.00", "5344.00, 37936.00", "5392.00, 37920.00", "5440.00, 37968.00", "5488.00, 37968.00", "5536.00, 37952.00", "5584.00, 37936.00", "5632.00, 37904.00", "5680.00, 37920.00", "5728.00, 37904.00", "5776.00, 37904.00", "5824.00, 37920.00", "5824.00, 37952.00", "5872.00, 37920.00", "5920.00, 37904.00", "5936.00, 37872.00", "5968.00, 37856.00", "6016.00, 37840.00", "6048.00, 37792.00", "6096.00, 37776.00", "6096.00, 37728.00", "6112.00, 37680.00", "6160.00, 37664.00", "6192.00, 37712.00", "6240.00, 37744.00", "6288.00, 37728.00", "6320.00, 37696.00", "6352.00, 37680.00", "6400.00, 37712.00", "6416.00, 37760.00", "6368.00, 37776.00", "6320.00, 37808.00", "6320.00, 37840.00", "6352.00, 37872.00", "6400.00, 37888.00", "6432.00, 37872.00", "6416.00, 37824.00", "6416.00, 37808.00", "6432.00, 37776.00", "6448.00, 37728.00", "6416.00, 37728.00", "6400.00, 37680.00", "6352.00, 37664.00", "6352.00, 37616.00", "6368.00, 37568.00", "6336.00, 37536.00", "6288.00, 37504.00", "6272.00, 37552.00", "6224.00, 37568.00", "6176.00, 37568.00", "6128.00, 37568.00", "6080.00, 37568.00", "6032.00, 37536.00", "5984.00, 37520.00", "5968.00, 37472.00", "5984.00, 37456.00"], "FillBackgroundColor": "", "FontColor": "#FFFF0000", "FontSize": 12.0, "ID": 10, "LineColor": "#FFFF0000", "LineWidth": 2.0, "Name": "root", "Opacity": 0, "PartOfGroup": "", "Tag": "add", "Type": "btn_brush"}, {"Area": 0.171, "Coordinates": ["5872.00, 37728.00", "5920.00, 37728.00", "5936.00, 37776.00", "5920.00, 37824.00", "5872.00, 37824.00", "5840.00, 37776.00", "5856.00, 37728.00", "5872.00, 37728.00"], "FillBackgroundColor": "", "FontColor": "#FFFF0000", "FontSize": 12.0, "ID": 13, "LineColor": "#FFFF0000", "LineWidth": 2.0, "Name": "root", "Opacity": 0, "PartOfGroup": "", "Tag": "add", "Type": "btn_brush"}, {"Area": 0.171, "Coordinates": ["6560.00, 36176.00", "6544.00, 36224.00", "6512.00, 36256.00", "6464.00, 36304.00", "6416.00, 36304.00", "6384.00, 36288.00", "6336.00, 36304.00", "6320.00, 36352.00", "6288.00, 36384.00", "6240.00, 36416.00", "6208.00, 36464.00", "6160.00, 36496.00", "6160.00, 36544.00", "6128.00, 36592.00", "6160.00, 36640.00", "6160.00, 36688.00", "6192.00, 36736.00", "6208.00, 36784.00", "6256.00, 36800.00", "6304.00, 36784.00", "6336.00, 36768.00", "6368.00, 36736.00", "6368.00, 36688.00", "6416.00, 36640.00", "6432.00, 36592.00", "6448.00, 36544.00", "6448.00, 36496.00", "6416.00, 36448.00", "6448.00, 36416.00", "6496.00, 36416.00", "6512.00, 36464.00", "6560.00, 36448.00", "6608.00, 36448.00", "6640.00, 36416.00", "6672.00, 36368.00", "6656.00, 36320.00", "6624.00, 36288.00", "6592.00, 36240.00", "6624.00, 36192.00", "6576.00, 36176.00", "6560.00, 36176.00"], "FillBackgroundColor": "", "FontColor": "#FFFF0000", "FontSize": 12.0, "ID": 18, "LineColor": "#FFFF0000", "LineWidth": 2.0, "Name": "root", "Opacity": 0, "PartOfGroup": "", "Tag": "add", "Type": "btn_brush"}, {"Area": 0.171, "Coordinates": ["6192.00, 36624.00", "6240.00, 36608.00", "6288.00, 36640.00", "6288.00, 36688.00", "6336.00, 36688.00", "6304.00, 36736.00", "6256.00, 36736.00", "6208.00, 36704.00", "6192.00, 36656.00", "6192.00, 36624.00"], "FillBackgroundColor": "", "FontColor": "#FFFF0000", "FontSize": 12.0, "ID": 20, "LineColor": "#FFFF0000", "LineWidth": 2.0, "Name": "root", "Opacity": 0, "PartOfGroup": "", "Tag": "add", "Type": "btn_brush"}, {"Area": 0.171, "Coordinates": ["5440.00, 36128.00", "5408.00, 36160.00", "5360.00, 36176.00", "5360.00, 36208.00", "5312.00, 36240.00", "5264.00, 36240.00", "5232.00, 36272.00", "5184.00, 36304.00", "5168.00, 36352.00", "5136.00, 36384.00", "5088.00, 36384.00", "5104.00, 36432.00", "5120.00, 36464.00", "5120.00, 36512.00", "5152.00, 36560.00", "5168.00, 36608.00", "5120.00, 36640.00", "5120.00, 36688.00", "5088.00, 36736.00", "5072.00, 36784.00", "5040.00, 36816.00", "5088.00, 36832.00", "5072.00, 36880.00", "5056.00, 36928.00", "5104.00, 36912.00", "5152.00, 36928.00", "5152.00, 36976.00", "5168.00, 37024.00", "5136.00, 37040.00", "5104.00, 37024.00", "5056.00, 36992.00", "5072.00, 37008.00", "5072.00, 37056.00", "5024.00, 37104.00", "4976.00, 37136.00", "4992.00, 37168.00", "5024.00, 37200.00", "4992.00, 37232.00", "4944.00, 37232.00", "4976.00, 37264.00", "4944.00, 37296.00", "4992.00, 37296.00", "5008.00, 37344.00", "5024.00, 37360.00", "5072.00, 37328.00", "5104.00, 37312.00", "5072.00, 37264.00", "5120.00, 37248.00", "5136.00, 37232.00", "5152.00, 37216.00", "5136.00, 37200.00", "5088.00, 37216.00", "5088.00, 37168.00", "5072.00, 37136.00", "5056.00, 37088.00", "5104.00, 37056.00", "5152.00, 37088.00", "5168.00, 37136.00", "5184.00, 37168.00", "5200.00, 37216.00", "5200.00, 37248.00", "5216.00, 37216.00", "5264.00, 37232.00", "5296.00, 37200.00", "5296.00, 37152.00", "5312.00, 37104.00", "5280.00, 37056.00", "5312.00, 37008.00", "5328.00, 36976.00", "5360.00, 36976.00", "5360.00, 36928.00", "5344.00, 36880.00", "5344.00, 36832.00", "5392.00, 36832.00", "5408.00, 36784.00", "5440.00, 36736.00", "5392.00, 36720.00", "5360.00, 36720.00", "5328.00, 36688.00", "5296.00, 36656.00", "5248.00, 36624.00", "5248.00, 36576.00", "5296.00, 36592.00", "5328.00, 36544.00", "5328.00, 36496.00", "5376.00, 36496.00", "5408.00, 36512.00", "5440.00, 36512.00", "5488.00, 36480.00", "5520.00, 36432.00", "5568.00, 36416.00", "5552.00, 36368.00", "5584.00, 36352.00", "5616.00, 36304.00", "5584.00, 36256.00", "5536.00, 36256.00", "5488.00, 36224.00", "5488.00, 36192.00", "5440.00, 36192.00", "5440.00, 36144.00", "5440.00, 36128.00"], "FillBackgroundColor": "", "FontColor": "#FFFF0000", "FontSize": 12.0, "ID": 21, "LineColor": "#FFFF0000", "LineWidth": 2.0, "Name": "root", "Opacity": 0, "PartOfGroup": "", "Tag": "add", "Type": "btn_brush"}, {"Area": 0.171, "Coordinates": ["5040.00, 37152.00", "5088.00, 37168.00", "5040.00, 37184.00", "5024.00, 37152.00", "5040.00, 37152.00"], "FillBackgroundColor": "", "FontColor": "#FFFF0000", "FontSize": 12.0, "ID": 24, "LineColor": "#FFFF0000", "LineWidth": 2.0, "Name": "root", "Opacity": 0, "PartOfGroup": "", "Tag": "add", "Type": "btn_brush"}, {"Area": 0.171, "Coordinates": ["5264.00, 36736.00", "5312.00, 36736.00", "5312.00, 36784.00", "5264.00, 36752.00", "5264.00, 36736.00"], "FillBackgroundColor": "", "FontColor": "#FFFF0000", "FontSize": 12.0, "ID": 28, "LineColor": "#FFFF0000", "LineWidth": 2.0, "Name": "root", "Opacity": 0, "PartOfGroup": "", "Tag": "add", "Type": "btn_brush"}, {"Area": 0.171, "Coordinates": ["5344.00, 36304.00", "5392.00, 36288.00", "5440.00, 36320.00", "5488.00, 36368.00", "5440.00, 36416.00", "5392.00, 36384.00", "5376.00, 36336.00", "5360.00, 36320.00", "5344.00, 36304.00"], "FillBackgroundColor": "", "FontColor": "#FFFF0000", "FontSize": 12.0, "ID": 32, "LineColor": "#FFFF0000", "LineWidth": 2.0, "Name": "root", "Opacity": 0, "PartOfGroup": "", "Tag": "add", "Type": "btn_brush"}, {"Area": 0.171, "Coordinates": ["9232.00, 36032.00", "9200.00, 36064.00", "9232.00, 36096.00", "9280.00, 36128.00", "9280.00, 36176.00", "9232.00, 36160.00", "9248.00, 36192.00", "9296.00, 36176.00", "9312.00, 36224.00", "9328.00, 36208.00", "9376.00, 36256.00", "9360.00, 36304.00", "9312.00, 36304.00", "9280.00, 36320.00", "9232.00, 36352.00", "9184.00, 36384.00", "9136.00, 36352.00", "9120.00, 36400.00", "9072.00, 36384.00", "9056.00, 36432.00", "9024.00, 36448.00", "9008.00, 36448.00", "8976.00, 36480.00", "8992.00, 36496.00", "9040.00, 36480.00", "9088.00, 36496.00", "9136.00, 36528.00", "9152.00, 36576.00", "9184.00, 36624.00", "9200.00, 36672.00", "9168.00, 36704.00", "9120.00, 36704.00", "9072.00, 36736.00", "9120.00, 36752.00", "9152.00, 36800.00", "9136.00, 36848.00", "9120.00, 36896.00", "9120.00, 36912.00", "9136.00, 36960.00", "9136.00, 37008.00", "9120.00, 37056.00", "9104.00, 37056.00", "9120.00, 37072.00", "9104.00, 37120.00", "9088.00, 37152.00", "9072.00, 37200.00", "9040.00, 37168.00", "9040.00, 37184.00", "9072.00, 37216.00", "9056.00, 37264.00", "9056.00, 37312.00", "9072.00, 37344.00", "9104.00, 37360.00", "9152.00, 37344.00", "9184.00, 37392.00", "9184.00, 37408.00", "9200.00, 37424.00", "9184.00, 37376.00", "9216.00, 37360.00", "9184.00, 37328.00", "9168.00, 37280.00", "9216.00, 37264.00", "9264.00, 37280.00", "9280.00, 37264.00", "9232.00, 37248.00", "9216.00, 37200.00", "9264.00, 37168.00", "9312.00, 37152.00", "9296.00, 37136.00", "9328.00, 37104.00", "9328.00, 37056.00", "9344.00, 37008.00", "9392.00, 37024.00", "9440.00, 37040.00", "9408.00, 37008.00", "9408.00, 36960.00", "9456.00, 36960.00", "9504.00, 36976.00", "9520.00, 37008.00", "9552.00, 36960.00", "9536.00, 36928.00", "9520.00, 36880.00", "9536.00, 36848.00", "9568.00, 36832.00", "9616.00, 36816.00", "9632.00, 36784.00", "9584.00, 36752.00", "9584.00, 36704.00", "9616.00, 36656.00", "9664.00, 36624.00", "9696.00, 36592.00", "9696.00, 36560.00", "9680.00, 36528.00", "9664.00, 36496.00", "9632.00, 36448.00", "9600.00, 36432.00", "9584.00, 36384.00", "9552.00, 36352.00", "9536.00, 36304.00", "9568.00, 36256.00", "9520.00, 36240.00", "9472.00, 36208.00", "9456.00, 36176.00", "9408.00, 36160.00", "9392.00, 36112.00", "9360.00, 36080.00", "9312.00, 36080.00", "9280.00, 36032.00", "9232.00, 36032.00"], "FillBackgroundColor": "", "FontColor": "#FFFF0000", "FontSize": 12.0, "ID": 34, "LineColor": "#FFFF0000", "LineWidth": 2.0, "Name": "root", "Opacity": 0, "PartOfGroup": "", "Tag": "add", "Type": "btn_brush"}, {"Area": 0.171, "Coordinates": ["9120.00, 37104.00", "9168.00, 37088.00", "9216.00, 37120.00", "9184.00, 37152.00", "9168.00, 37200.00", "9152.00, 37248.00", "9120.00, 37216.00", "9136.00, 37168.00", "9120.00, 37120.00", "9120.00, 37104.00"], "FillBackgroundColor": "", "FontColor": "#FFFF0000", "FontSize": 12.0, "ID": 38, "LineColor": "#FFFF0000", "LineWidth": 2.0, "Name": "root", "Opacity": 0, "PartOfGroup": "", "Tag": "add", "Type": "btn_brush"}, {"Area": 0.171, "Coordinates": ["9200.00, 36960.00", "9248.00, 36944.00", "9280.00, 36992.00", "9248.00, 37024.00", "9200.00, 37008.00", "9200.00, 36960.00"], "FillBackgroundColor": "", "FontColor": "#FFFF0000", "FontSize": 12.0, "ID": 39, "LineColor": "#FFFF0000", "LineWidth": 2.0, "Name": "root", "Opacity": 0, "PartOfGroup": "", "Tag": "add", "Type": "btn_brush"}, {"Area": 0.171, "Coordinates": ["9136.00, 36400.00", "9184.00, 36384.00", "9216.00, 36416.00", "9232.00, 36464.00", "9200.00, 36496.00", "9152.00, 36512.00", "9104.00, 36480.00", "9088.00, 36432.00", "9120.00, 36400.00", "9136.00, 36400.00"], "FillBackgroundColor": "", "FontColor": "#FFFF0000", "FontSize": 12.0, "ID": 46, "LineColor": "#FFFF0000", "LineWidth": 2.0, "Name": "root", "Opacity": 0, "PartOfGroup": "", "Tag": "add", "Type": "btn_brush"}, {"Area": 0.171, "Coordinates": ["9344.00, 36096.00", "9376.00, 36112.00", "9392.00, 36160.00", "9344.00, 36160.00", "9296.00, 36128.00", "9344.00, 36096.00"], "FillBackgroundColor": "", "FontColor": "#FFFF0000", "FontSize": 12.0, "ID": 49, "LineColor": "#FFFF0000", "LineWidth": 2.0, "Name": "root", "Opacity": 0, "PartOfGroup": "", "Tag": "add", "Type": "btn_brush"}, {"Area": 0.171, "Coordinates": ["5728.00, 32976.00", "5696.00, 33024.00", "5664.00, 33072.00", "5632.00, 33088.00", "5584.00, 33120.00", "5584.00, 33168.00", "5600.00, 33216.00", "5568.00, 33248.00", "5520.00, 33264.00", "5504.00, 33312.00", "5488.00, 33360.00", "5440.00, 33344.00", "5392.00, 33344.00", "5344.00, 33312.00", "5376.00, 33264.00", "5424.00, 33248.00", "5472.00, 33232.00", "5472.00, 33216.00", "5424.00, 33184.00", "5392.00, 33184.00", "5408.00, 33232.00", "5360.00, 33264.00", "5328.00, 33216.00", "5344.00, 33168.00", "5376.00, 33152.00", "5360.00, 33136.00", "5360.00, 33088.00", "5344.00, 33072.00", "5296.00, 33056.00", "5248.00, 33072.00", "5200.00, 33072.00", "5152.00, 33056.00", "5104.00, 33072.00", "5056.00, 33088.00", "5008.00, 33104.00", "4960.00, 33120.00", "4944.00, 33168.00", "4896.00, 33200.00", "4864.00, 33248.00", "4864.00, 33296.00", "4832.00, 33344.00", "4816.00, 33392.00", "4800.00, 33440.00", "4784.00, 33488.00", "4768.00, 33536.00", "4784.00, 33584.00", "4832.00, 33600.00", "4816.00, 33552.00", "4832.00, 33520.00", "4848.00, 33488.00", "4880.00, 33440.00", "4928.00, 33456.00", "4976.00, 33440.00", "5024.00, 33408.00", "5056.00, 33360.00", "5072.00, 33312.00", "5120.00, 33280.00", "5152.00, 33248.00", "5184.00, 33280.00", "5184.00, 33312.00", "5232.00, 33296.00", "5280.00, 33280.00", "5296.00, 33328.00", "5312.00, 33376.00", "5264.00, 33392.00", "5216.00, 33376.00", "5168.00, 33360.00", "5152.00, 33408.00", "5120.00, 33456.00", "5104.00, 33504.00", "5056.00, 33520.00", "5008.00, 33520.00", "4960.00, 33520.00", "4928.00, 33536.00", "4896.00, 33568.00", "4864.00, 33616.00", "4848.00, 33664.00", "4816.00, 33664.00", "4800.00, 33712.00", "4816.00, 33744.00", "4800.00, 33696.00", "4848.00, 33664.00", "4896.00, 33712.00", "4848.00, 33744.00", "4864.00, 33792.00", "4880.00, 33840.00", "4864.00, 33888.00", "4848.00, 33936.00", "4832.00, 33984.00", "4816.00, 34032.00", "4768.00, 34080.00", "4736.00, 34112.00", "4720.00, 34144.00", "4720.00, 34192.00", "4752.00, 34208.00", "4800.00, 34224.00", "4848.00, 34224.00", "4896.00, 34224.00", "4944.00, 34224.00", "4960.00, 34192.00", "4976.00, 34144.00", "4960.00, 34096.00", "4960.00, 34048.00", "4912.00, 34064.00", "4880.00, 34016.00", "4912.00, 33984.00", "4960.00, 33968.00", "4992.00, 34000.00", "5024.00, 34032.00", "5040.00, 34080.00", "4992.00, 34096.00", "5040.00, 34112.00", "5056.00, 34096.00", "5088.00, 34112.00", "5088.00, 34160.00", "5088.00, 34208.00", "5120.00, 34192.00", "5168.00, 34208.00", "5184.00, 34160.00", "5200.00, 34112.00", "5184.00, 34064.00", "5168.00, 34016.00", "5168.00, 33968.00", "5184.00, 33920.00", "5216.00, 33872.00", "5216.00, 33840.00", "5200.00, 33872.00", "5152.00, 33904.00", "5136.00, 33872.00", "5152.00, 33824.00", "5200.00, 33792.00", "5248.00, 33776.00", "5264.00, 33760.00", "5296.00, 33776.00", "5264.00, 33728.00", "5312.00, 33696.00", "5280.00, 33712.00", "5232.00, 33728.00", "5184.00, 33744.00", "5136.00, 33728.00", "5152.00, 33680.00", "5184.00, 33648.00", "5200.00, 33600.00", "5232.00, 33568.00", "5280.00, 33536.00", "5328.00, 33504.00", "5376.00, 33520.00", "5408.00, 33488.00", "5424.00, 33520.00", "5424.00, 33568.00", "5408.00, 33616.00", "5408.00, 33664.00", "5424.00, 33616.00", "5472.00, 33568.00", "5472.00, 33520.00", "5488.00, 33472.00", "5504.00, 33424.00", "5552.00, 33424.00", "5568.00, 33472.00", "5600.00, 33488.00", "5568.00, 33536.00", "5568.00, 33552.00", "5600.00, 33520.00", "5616.00, 33520.00", "5616.00, 33472.00", "5648.00, 33456.00", "5696.00, 33424.00", "5744.00, 33440.00", "5776.00, 33440.00", "5824.00, 33392.00", "5872.00, 33376.00", "5856.00, 33424.00", "5840.00, 33472.00", "5824.00, 33520.00", "5792.00, 33552.00", "5808.00, 33584.00", "5824.00, 33632.00", "5840.00, 33680.00", "5856.00, 33696.00", "5888.00, 33712.00", "5888.00, 33760.00", "5856.00, 33808.00", "5824.00, 33856.00", "5824.00, 33904.00", "5792.00, 33952.00", "5776.00, 34000.00", "5744.00, 34032.00", "5696.00, 34048.00", "5664.00, 34096.00", "5664.00, 34144.00", "5712.00, 34112.00", "5760.00, 34112.00", "5792.00, 34128.00", "5744.00, 34160.00", "5744.00, 34192.00", "5776.00, 34224.00", "5824.00, 34224.00", "5872.00, 34224.00", "5920.00, 34224.00", "5968.00, 34224.00", "6016.00, 34224.00", "6064.00, 34224.00", "6096.00, 34208.00", "6144.00, 34224.00", "6192.00, 34224.00", "6240.00, 34224.00", "6288.00, 34224.00", "6288.00, 34192.00", "6240.00, 34176.00", "6192.00, 34144.00", "6160.00, 34112.00", "6128.00, 34080.00", "6112.00, 34032.00", "6112.00, 33984.00", "6064.00, 33952.00", "6080.00, 33904.00", "6064.00, 33856.00", "6080.00, 33808.00", "6096.00, 33760.00", "6080.00, 33712.00", "6112.00, 33664.00", "6128.00, 33616.00", "6128.00, 33568.00", "6112.00, 33520.00", "6128.00, 33472.00", "6144.00, 33424.00", "6176.00, 33376.00", "6224.00, 33344.00", "6192.00, 33296.00", "6144.00, 33280.00", "6144.00, 33232.00", "6112.00, 33184.00", "6080.00, 33200.00", "6096.00, 33248.00", "6048.00, 33248.00", "6016.00, 33200.00", "5968.00, 33168.00", "5952.00, 33120.00", "5920.00, 33088.00", "5872.00, 33120.00", "5904.00, 33168.00", "5888.00, 33200.00", "5872.00, 33248.00", "5856.00, 33296.00", "5808.00, 33344.00", "5776.00, 33392.00", "5728.00, 33360.00", "5760.00, 33312.00", "5744.00, 33264.00", "5696.00, 33232.00", "5696.00, 33184.00", "5728.00, 33136.00", "5776.00, 33088.00", "5824.00, 33088.00", "5824.00, 33040.00", "5792.00, 33024.00", "5744.00, 32992.00", "5728.00, 32976.00"], "FillBackgroundColor": "", "FontColor": "#FFFF0000", "FontSize": 12.0, "ID": 50, "LineColor": "#FFFF0000", "LineWidth": 2.0, "Name": "root", "Opacity": 0, "PartOfGroup": "", "Tag": "add", "Type": "btn_brush"}, {"Area": 0.171, "Coordinates": ["5952.00, 34016.00", "6000.00, 34000.00", "6048.00, 34000.00", "6080.00, 34032.00", "6032.00, 34064.00", "6032.00, 34096.00", "6000.00, 34128.00", "5952.00, 34144.00", "5920.00, 34112.00", "5920.00, 34064.00", "5952.00, 34016.00"], "FillBackgroundColor": "", "FontColor": "#FFFF0000", "FontSize": 12.0, "ID": 53, "LineColor": "#FFFF0000", "LineWidth": 2.0, "Name": "root", "Opacity": 0, "PartOfGroup": "", "Tag": "add", "Type": "btn_brush"}, {"Area": 0.171, "Coordinates": ["6032.00, 33792.00", "6064.00, 33808.00", "6032.00, 33856.00", "6000.00, 33808.00", "6032.00, 33792.00"], "FillBackgroundColor": "", "FontColor": "#FFFF0000", "FontSize": 12.0, "ID": 55, "LineColor": "#FFFF0000", "LineWidth": 2.0, "Name": "root", "Opacity": 0, "PartOfGroup": "", "Tag": "add", "Type": "btn_brush"}, {"Area": 0.171, "Coordinates": ["5888.00, 33792.00", "5936.00, 33792.00", "5936.00, 33840.00", "5952.00, 33888.00", "5952.00, 33936.00", "5952.00, 33920.00", "6000.00, 33904.00", "6000.00, 33952.00", "5968.00, 33984.00", "5920.00, 34000.00", "5872.00, 34000.00", "5872.00, 34048.00", "5824.00, 34096.00", "5776.00, 34064.00", "5792.00, 34016.00", "5824.00, 33968.00", "5824.00, 33920.00", "5840.00, 33872.00", "5888.00, 33824.00", "5888.00, 33792.00"], "FillBackgroundColor": "", "FontColor": "#FFFF0000", "FontSize": 12.0, "ID": 56, "LineColor": "#FFFF0000", "LineWidth": 2.0, "Name": "root", "Opacity": 0, "PartOfGroup": "", "Tag": "add", "Type": "btn_brush"}, {"Area": 0.171, "Coordinates": ["4896.00, 33792.00", "4928.00, 33808.00", "4928.00, 33856.00", "4880.00, 33840.00", "4896.00, 33792.00"], "FillBackgroundColor": "", "FontColor": "#FFFF0000", "FontSize": 12.0, "ID": 57, "LineColor": "#FFFF0000", "LineWidth": 2.0, "Name": "root", "Opacity": 0, "PartOfGroup": "", "Tag": "add", "Type": "btn_brush"}, {"Area": 0.171, "Coordinates": ["5856.00, 33568.00", "5888.00, 33584.00", "5872.00, 33632.00", "5840.00, 33616.00", "5856.00, 33568.00"], "FillBackgroundColor": "", "FontColor": "#FFFF0000", "FontSize": 12.0, "ID": 59, "LineColor": "#FFFF0000", "LineWidth": 2.0, "Name": "root", "Opacity": 0, "PartOfGroup": "", "Tag": "add", "Type": "btn_brush"}, {"Area": 0.171, "Coordinates": ["5168.00, 33552.00", "5200.00, 33568.00", "5168.00, 33616.00", "5136.00, 33584.00", "5168.00, 33552.00"], "FillBackgroundColor": "", "FontColor": "#FFFF0000", "FontSize": 12.0, "ID": 60, "LineColor": "#FFFF0000", "LineWidth": 2.0, "Name": "root", "Opacity": 0, "PartOfGroup": "", "Tag": "add", "Type": "btn_brush"}, {"Area": 0.171, "Coordinates": ["5920.00, 33424.00", "5968.00, 33424.00", "5952.00, 33472.00", "5920.00, 33504.00", "5872.00, 33488.00", "5904.00, 33440.00", "5920.00, 33424.00"], "FillBackgroundColor": "", "FontColor": "#FFFF0000", "FontSize": 12.0, "ID": 67, "LineColor": "#FFFF0000", "LineWidth": 2.0, "Name": "root", "Opacity": 0, "PartOfGroup": "", "Tag": "add", "Type": "btn_brush"}, {"Area": 0.171, "Coordinates": ["5632.00, 33216.00", "5680.00, 33216.00", "5680.00, 33264.00", "5632.00, 33264.00", "5632.00, 33216.00"], "FillBackgroundColor": "", "FontColor": "#FFFF0000", "FontSize": 12.0, "ID": 70, "LineColor": "#FFFF0000", "LineWidth": 2.0, "Name": "root", "Opacity": 0, "PartOfGroup": "", "Tag": "add", "Type": "btn_brush"}, {"Area": 0.171, "Coordinates": ["5920.00, 33200.00", "5968.00, 33184.00", "5968.00, 33232.00", "5936.00, 33280.00", "5904.00, 33296.00", "5904.00, 33248.00", "5920.00, 33200.00"], "FillBackgroundColor": "", "FontColor": "#FFFF0000", "FontSize": 12.0, "ID": 71, "LineColor": "#FFFF0000", "LineWidth": 2.0, "Name": "root", "Opacity": 0, "PartOfGroup": "", "Tag": "add", "Type": "btn_brush"}, {"Area": 0.171, "Coordinates": ["8000.00, 29408.00", "8000.00, 29456.00", "8048.00, 29472.00", "8096.00, 29504.00", "8144.00, 29488.00", "8160.00, 29440.00", "8192.00, 29488.00", "8208.00, 29536.00", "8240.00, 29536.00", "8208.00, 29488.00", "8208.00, 29440.00", "8160.00, 29408.00", "8112.00, 29424.00", "8064.00, 29424.00", "8016.00, 29408.00", "8000.00, 29408.00"], "FillBackgroundColor": "", "FontColor": "#FFFF0000", "FontSize": 12.0, "ID": 79, "LineColor": "#FFFF0000", "LineWidth": 2.0, "Name": "root", "Opacity": 0, "PartOfGroup": "", "Tag": "add", "Type": "btn_brush"}], "Name": "Invasive_Area"}, {"Color": "", "Groups": [], "Labels": [], "Name": "ROI"}], "Labels": [], "Name": "root"}, "ClassifyRoot": null, "RedBloodCellQualitativeInfos": null, "IHCResult": {"wsi_score": 0.0, "total_p": 0, "total_n": 175, "ic_score": 0.0, "ic_p": 0, "ic_n": 155, "dcis_score": 0.0, "dcis_p": 0, "dcis_n": 20, "roi_score": 0}}